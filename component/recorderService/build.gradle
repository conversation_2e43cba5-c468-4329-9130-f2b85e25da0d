apply from:"../../common_build.gradle"

android {

    sourceSets {
        main {
            aidl.srcDirs = ['src/main/java']
            jniLibs.srcDirs = ['libs']
        }
    }
    namespace "com.soundrecorder.recorderservice"
    buildFeatures {
        aidl true
    }
}

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.material
    implementation libs.androidx.appcompat
    implementation libs.androidx.lifecycle.livedata
    implementation libs.androidx.lifecycle.viewmodel
    implementation libs.androidx.lifecycle.extensions
    implementation libs.androidx.fragment.ktx
    compileOnly libs.oplus.addon
    testImplementation libs.oplus.addon

    // Koin for Android
    implementation(libs.koin)

    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')
    implementation project(':component:wavemark')
    implementation project(':component:translate')
    // OS16以上版本全息屏刷新需要用到keyguardStyle和appplatform 以下两个SDK
    implementation "com.oplus.keyguard:keyguardStyle:2.0.3-alphaa96aaa4"
    implementation "com.oplus.appplatform:sysapi:13.0.4"
}