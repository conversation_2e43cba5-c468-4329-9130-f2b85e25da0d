<?xml version="1.0" encoding="utf-8"?>
<com.soundrecorder.common.widget.ClickScaleCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dialog_mark_item_height"
    android:forceDarkAllowed="false"
    android:elevation="0dp"
    android:background="@drawable/dialog_item_background"
    android:clickable="true"
    android:longClickable="true"
    app:cardCornerRadius="@dimen/dp10"
    app:couiCardCornerWeight="@dimen/coui_round_corner_m_weight"
    app:exclude_view_tags="btnMenuMore,img_mark_photo">

    <!-- 第一行 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/first_line_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginTop="@dimen/dp13"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp24"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_mark_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="@dimen/dialog_mark_item_image_size"
            android:layout_height="@dimen/dialog_mark_item_image_size"
            android:src="@drawable/ic_mark_dialog"
            android:contentDescription="@string/talkback_flag"/>

        <TextView
            android:id="@+id/text_mark_time"
            app:layout_constraintStart_toEndOf="@+id/iv_mark_icon"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp6"
            android:fontFamily="sys-sans-en"
            android:fontFeatureSettings="tnum"
            android:textFontWeight="500"
            android:textColor="@color/coui_color_primary_neutral"
            android:textSize="@dimen/dp16"
            tools:text="00:03" />

        <ImageButton
            android:id="@+id/btnMenuMore"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="@dimen/dp24"
            android:layout_height="@dimen/dp24"
            android:background="@drawable/menu_more_pressed"
            android:contentDescription="@string/abc_action_menu_overflow_description"
            android:foreground="@null"
            android:scaleType="center"
            android:src="@drawable/icon_mark_more"
            android:tag="btnMenuMore" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/subinfo_line_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp13"
        android:layout_height="@dimen/dp30"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/text_mark_description"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:ellipsize="end"
            android:fontFamily="sans-serif-medium"
            android:gravity="center_vertical|start"
            android:maxLines="1"
            android:textAlignment="viewStart"
            android:textColor="@color/coui_color_primary_neutral"
            android:textSize="@dimen/dp16"
            tools:text="标记 2" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/img_mark_photo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="@dimen/dp30"
            android:layout_height="match_parent"
            android:contentDescription="@string/talkback_preview_mark_picture"
            android:forceDarkAllowed="false"
            android:foreground="@null"
            android:padding="6dp"
            android:scaleType="centerCrop"
            android:tag="img_mark_photo"
            android:visibility="visible"
            app:strokeColor="@color/picture_mark_icon_bord_color"
            app:strokeWidth="0.5dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.soundrecorder.common.widget.ClickScaleCardView>