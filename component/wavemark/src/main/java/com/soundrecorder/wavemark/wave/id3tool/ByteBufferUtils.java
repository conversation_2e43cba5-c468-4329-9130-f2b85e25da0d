
package com.soundrecorder.wavemark.wave.id3tool;

import java.nio.ByteBuffer;

import com.soundrecorder.common.constant.Constants;

public class ByteBufferUtils {

    public static String extractNullTerminatedString(ByteBuffer bb) {
        int start = bb.position();

        byte[] buffer = new byte[bb.remaining()];

        bb.get(buffer);

        String s = new String(buffer, Constants.UTF_8);
        int nullPos = s.indexOf(0);

        s = s.substring(0, nullPos);

        bb.position(start + s.length() + 1);

        return s;
    }

}
