package com.recorder.movepure

import com.soundrecorder.common.databean.KeyWord
import com.soundrecorder.common.db.KeyWordDbUtils

class KeyWordParser : BaseXmlParser<KeyWord>() {

    private val KEY_WORD_XML = "key_word.xml"

    override fun getXmlName(): String = KEY_WORD_XML

    override fun createData(): KeyWord {

        return KeyWord("", 0f)
    }

    override fun setDataAttr(data: KeyWord, name: String, value: String) {
        when (name) {
            KeyWordDbUtils.RECORD_ID -> {
                data.recordId = value.toLong()
            }
            KeyWordDbUtils.MEDIA_PATH -> {
                data.mediaPath = value
            }
            KeyWordDbUtils.NAME -> {
                data.name = value
            }
            KeyWordDbUtils.TFIDF_VALUE -> {
                data.tfidfvalue = value.toFloat()
            }
        }
    }
}