一.录音卖场资源包结构（截至至摘要需求）
【文件名会经过UrlEncodeUtil.encode加密，在拷贝创建新文件前，需要decode】
/data/oplus/common/screensavers/包名/
+--- Recordings
|    +--- "Standard Recordings"
|    |    +--- "标准录音 1.mp3"    -----> 会被拷贝到/storage/emulated/0/Music/Recordings/Standard Recordings目录下
|    |    +--- "标准录音 1.txt"    -----> 会被拷贝到/data/data/包名/files/convert目录下
|    |    \--- "标准录音 1.json"    -----> 摘要的配置文件，会被解析并插入到summary数据库中，供跳转到便签
|    +--- "Interview Recordings"
|    |    +--- "标准录音 2.mp3"
|    |    +--- "标准录音 2.txt"
|    |    \--- "标准录音 2.json"
|    +--- "Meeting Recordings"
|    |    +--- "标准录音 3.mp3"
|    |    +--- "标准录音 3.txt"
|    |    \--- "标准录音 3.json"
|    \--- "Call Recordings"
|         +--- "标准录音 4.mp3"
|         +--- "标准录音 4.txt"
|         \--- "标准录音 4.json"
+--- DE（德国地区）
|    \--- Recordings
|         +--- "Standard Recordings"
|         |    +--- "标准录音 1.mp3"    -----> 会被拷贝到/storage/emulated/0/Music/Recordings/Standard Recordings目录下
|         |    +--- "标准录音 1.txt"    -----> 会被拷贝到/data/data/包名/files/convert目录下
|         |    \--- "标准录音 1.json"    -----> 摘要的配置文件，会被解析并插入到summary数据库中，供跳转到便签
|         +--- "Interview Recordings"
|         |    +--- "标准录音 2.mp3"
|         |    +--- "标准录音 2.txt"
|         |    \--- "标准录音 2.json"
|         +--- "Meeting Recordings"
|         |    +--- "标准录音 3.mp3"
|         |    +--- "标准录音 3.txt"
|         |    \--- "标准录音 3.json"
|         \--- "Call Recordings"
|              +--- "标准录音 4.mp3"
|              +--- "标准录音 4.txt"
|              \--- "标准录音 4.json"
\--- GB（英国地区）
     \--- Recordings
          +--- "Standard Recordings"
          |    +--- "标准录音 1.mp3"    -----> 会被拷贝到/storage/emulated/0/Music/Recordings/Standard Recordings目录下
          |    +--- "标准录音 1.txt"    -----> 会被拷贝到/data/data/包名/files/convert目录下
          |    \--- "标准录音 1.json"    -----> 摘要的配置文件，会被解析并插入到summary数据库中，供跳转到便签
          +--- "Interview Recordings"
          |    +--- "标准录音 2.mp3"
          |    +--- "标准录音 2.txt"
          |    \--- "标准录音 2.json"
          +--- "Meeting Recordings"
          |    +--- "标准录音 3.mp3"
          |    +--- "标准录音 3.txt"
          |    \--- "标准录音 3.json"
          \--- "Call Recordings"
               +--- "标准录音 4.mp3"
               +--- "标准录音 4.txt"
               \--- "标准录音 4.json"

二.摘要json格式及内容说明
{
  "record_uuid": "f8a430e4-1b9f-43c7-8f36-90e00b2b4727", // 所有录音的uuid，值非空
  "recordType": 6, //6：录音-非通话录音摘要，值非空
  "note_id": "8567432b-0cb7-4224-8bde-baa4a1b64e3e", //便签生成摘要数据id，值非空
  "media_id": "", // 音频文件媒体库ID，值可为空
  "media_path": "" //音频文件路径，值可为空
}
比如：
{
  "record_uuid": "f8a430e4-1b9f-43c7-8f36-90e00b2b4727",
  "recordType": 6,
  "note_id": "8567432b-0cb7-4224-8bde-baa4a1b64e3e",
  "media_id": "",
  "media_path": ""
}



