/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ScreenUtilTest
 Description:
 Version: 1.0
 Date: 2022/9/22
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/10/14 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.utils

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.soundrecorder.breenocardlibrary.utils.ScreenUtil.dp2px
import com.oplus.soundrecorder.breenocardlibrary.utils.ScreenUtil.isNightMode
import com.oplus.soundrecorder.breenocardlibrary.utils.ScreenUtil.px2dp
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
class ScreenUtilTest {

    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun dp2px() {
        mContext?.dp2px(10)
    }

    @Test
    fun px2dp() {
        mContext?.px2dp(10)
    }

    @Test
    fun isNightMode() {
        mContext?.isNightMode()
    }
}