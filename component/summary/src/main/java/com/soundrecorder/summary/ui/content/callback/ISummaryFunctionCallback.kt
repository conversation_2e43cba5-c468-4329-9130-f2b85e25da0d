/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ISummaryFunctionCallback
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content.callback

import android.view.View
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.model.SummaryTheme

interface ISummaryFunctionCallback {

    enum class ExportType {
        TO_WORD, TO_PDF, TO_NOTE
    }

    fun onClickAgent(agents: List<SummaryAgentEvent>) {}

    fun onClickEntity(view: View, entity: SummaryEntity) {}

    fun onClickEntityCard(view: View, entity: SummaryEntity) {}

    fun onClickExport(type: ExportType) {}

    fun onClickRefresh() {}

    fun onClickRetry() {}

    fun onClickPrevious() {}

    fun onClickScene(theme: SummaryTheme?) {}

    fun onClickNext() {}

    fun onClickCopy() {}

    fun onFinishAnimator() {}
}