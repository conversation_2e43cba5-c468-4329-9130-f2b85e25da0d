/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ISummaryStateChangeListener
 * Description:
 * Version: 1.0
 * Date: 2024/3/7
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/7 1.0 create
 */

package com.soundrecorder.summary.client

import com.soundrecorder.summary.data.SummaryStateResult

interface ISummaryStateChangeListener {

    fun onSummaryStateChanged(state: Int, type: Int, result: SummaryStateResult?)
}