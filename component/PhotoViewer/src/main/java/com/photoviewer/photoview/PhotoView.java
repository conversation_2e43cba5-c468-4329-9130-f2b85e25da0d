/*
 Copyright 2011, 2012 <PERSON>.
 <p>
 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at
 <p>
 http://www.apache.org/licenses/LICENSE-2.0
 <p>
 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
 */
package com.photoviewer.photoview;

import android.content.Context;
import android.graphics.Matrix;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.util.AttributeSet;
import android.view.GestureDetector;

import androidx.appcompat.widget.AppCompatImageView;

/**
 * A zoomable ImageView. See {@link PhotoViewController} for most of the details on how the zooming
 * is accomplished
 */
@SuppressWarnings("unused")
public class PhotoView extends AppCompatImageView {
    public static final int HORIZONTAL = 0;
    public static final int VERTICAL = 1;
    private PhotoViewController phoViewController;
    private ScaleType pendingScaleType;
    private int mParentScrollDirection = HORIZONTAL;

    public PhotoView(Context context) {
        this(context, null);
    }

    public PhotoView(Context context, AttributeSet attr) {
        this(context, attr, 0);
    }

    public PhotoView(Context context, AttributeSet attr, int defStyle) {
        super(context, attr, defStyle);
        init();
    }

    private void init() {
        phoViewController = new PhotoViewController(this);
        //We always pose as a Matrix scale type, though we can change to another scale type
        //via the attacher
        super.setScaleType(ScaleType.MATRIX);
        //apply the previously applied scale type
        if (pendingScaleType != null) {
            setScaleType(pendingScaleType);
            pendingScaleType = null;
        }
    }

    /**
     * Get the current {@link PhotoViewController} for this view. Be wary of holding on to references
     * to this attacher, as it has a reference to this view, which, if a reference is held in the
     * wrong place, can cause memory leaks.
     *
     * @return the attacher.
     */
    public PhotoViewController getPhoViewController() {
        return phoViewController;
    }

    @Override
    public ScaleType getScaleType() {
        return phoViewController.getScaleType();
    }

    @Override
    public Matrix getImageMatrix() {
        return phoViewController.getImageMatrix();
    }

    @Override
    public void setScaleType(ScaleType scaleType) {
        if (phoViewController == null) {
            pendingScaleType = scaleType;
        } else {
            phoViewController.setScaleType(scaleType);
        }
    }

    @Override
    public void setImageDrawable(Drawable drawable) {
        super.setImageDrawable(drawable);
        // setImageBitmap calls through to this method
        if (phoViewController != null) {
            phoViewController.update();
        }
    }

    @Override
    public void setImageResource(int resId) {
        super.setImageResource(resId);
        if (phoViewController != null) {
            phoViewController.update();
        }
    }

    @Override
    public void setImageURI(Uri uri) {
        super.setImageURI(uri);
        if (phoViewController != null) {
            phoViewController.update();
        }
    }

    @Override
    protected boolean setFrame(int l, int t, int r, int b) {
        boolean changed = super.setFrame(l, t, r, b);
        if (changed) {
            phoViewController.update();
        }
        return changed;
    }

    public boolean isZoomable() {
        return phoViewController.isZoomable();
    }

    public void setZoomable(boolean zoomable) {
        phoViewController.setZoomable(zoomable);
    }

    public RectF getDisplayRect() {
        return phoViewController.getDisplayRect();
    }

    public void getDisplayMatrix(Matrix matrix) {
        phoViewController.getDisplayMatrix(matrix);
    }

    @SuppressWarnings("UnusedReturnValue")
    public boolean setDisplayMatrix(Matrix finalRectangle) {
        return phoViewController.setDisplayMatrix(finalRectangle);
    }

    public void getSuppMatrix(Matrix matrix) {
        phoViewController.getSuppMatrix(matrix);
    }

    public boolean setSuppMatrix(Matrix matrix) {
        return phoViewController.setDisplayMatrix(matrix);
    }

    public float getMinimumScale() {
        return phoViewController.getMinimumScale();
    }

    public float getMediumScale() {
        return phoViewController.getMediumScale();
    }

    public float getMaximumScale() {
        return phoViewController.getMaximumScale();
    }

    public float getScale() {
        return phoViewController.getScale();
    }

    public void setAllowParentInterceptOnEdge(boolean allow) {
        phoViewController.setAllowParentInterceptOnEdge(allow);
    }

    public void setMinimumScale(float minimumScale) {
        phoViewController.setMinimumScale(minimumScale);
    }

    public void setMediumScale(float mediumScale) {
        phoViewController.setMediumScale(mediumScale);
    }

    public void setMaximumScale(float maximumScale) {
        phoViewController.setMaximumScale(maximumScale);
    }

    public void setScaleLevels(float minimumScale, float mediumScale, float maximumScale) {
        phoViewController.setScaleLevels(minimumScale, mediumScale, maximumScale);
    }

    public void setScale(float scale) {
        phoViewController.setScale(scale);
    }

    public void setScale(float scale, boolean animate) {
        phoViewController.setScale(scale, animate);
    }

    public void setScale(float scale, float focalX, float focalY, boolean animate) {
        phoViewController.setScale(scale, focalX, focalY, animate);
    }

    public void  resetScale(boolean animate){
        phoViewController.setScale(getMinimumScale(), animate);
    }

    public void setZoomTransitionDuration(int milliseconds) {
        phoViewController.setZoomTransitionDuration(milliseconds);
    }

    public void setOnDoubleTapListener(GestureDetector.OnDoubleTapListener onDoubleTapListener) {
        phoViewController.setOnDoubleTapListener(onDoubleTapListener);
    }

    public int getParentScrollDirection() {
        return mParentScrollDirection;
    }

    public void setParentScrollDirection(int parentScrollDirection) {
        mParentScrollDirection = parentScrollDirection;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        phoViewController.cancelFlingAndZoom();
    }
}
