/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BaseConvert
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.bean

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.BeanConvertText

open class BeanConvert(
    code: Int,
    msg: String,
    serverPlanCode: Int,
    traceId: String,
    showSwitch: Boolean?,
    val data: BeanConvertText
) : BeanConvertBase(code, msg, serverPlanCode, traceId, showSwitch) {
    companion object {
        const val TAG = "BeanConvert"
        const val MS = 1000L
    }

    constructor(beanConvertBase: BeanConvertBase, data: BeanConvertText) : this(
            code = beanConvertBase.code,
            msg = beanConvertBase.msg,
            serverPlanCode = beanConvertBase.serverPlanCode,
            traceId = beanConvertBase.traceId,
            showSwitch = beanConvertBase.showSwitch,
            data = data)

    override fun print() {
        DebugUtil.i(
            TAG, "code: $code, " +
                "msg = $msg,serverPlanCode = $serverPlanCode," +
                "traceId=$traceId," +
                "data=$data ," +
                "showSwitch = $showSwitch"
        )
    }
}
