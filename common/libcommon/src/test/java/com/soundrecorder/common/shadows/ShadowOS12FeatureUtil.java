package com.soundrecorder.common.shadows;

import com.soundrecorder.base.utils.OS12FeatureUtil;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(OS12FeatureUtil.class)
public class ShadowOS12FeatureUtil {

    @Implementation
    public static boolean isSuperSoundRecorderEpicEffective() {
        return true;
    }


    @Implementation
    public static boolean readFeatureByOplusFeature() {
        return true;
    }


    @Implementation
    public static boolean isFindX4AndNotConfidential() {
        return true;
    }

    @Implementation
    public static boolean isFindX4() {
        return true;
    }

    @Implementation
    public static boolean isColorOs12() {
        return false;
    }

    @Implementation
    public static boolean isColorOS14Point1OrLater() {
        return false;
    }
}
