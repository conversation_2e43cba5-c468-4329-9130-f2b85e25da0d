/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: DisplayUtilsText
 Description:
 Version: 1.0
 Date: 2022/9/23
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/9/23 1.0 create
 */

package com.soundrecorder.common.utils

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.utils.DisplayUtils.isDefaultDisplay
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class DisplayUtilsTest {

    @Test
    fun test() {
        val d = DisplayUtils.currentDisplay()
        Assert.assertNotNull(d)
        Assert.assertTrue(d.isDefaultDisplay())
        Assert.assertNotNull(DisplayUtils.otherDisplay())
    }
}
