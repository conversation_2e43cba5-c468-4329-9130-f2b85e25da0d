/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ConvertDbUtil.java
 * * Description: ConvertDbUtil.java
 * * Version: 1.0
 * * Date : 2019/9/25
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * yixiaoming  2019/9/25      1.0    build this module
 ****************************************************************/

package com.soundrecorder.common.utils;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.database.Cursor;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.StorageManager;
import com.soundrecorder.base.utils.ChunkNameUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.NumberConstant;
import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.databean.ConvertRecord;
import com.soundrecorder.common.databean.ConvertStatus;
import com.soundrecorder.common.databean.UploadRecord;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.db.UploadDbUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class ConvertDbUtil {

    public static final String TAG = "ConvertDbUtil";

    public static final int CONVERT_COMP_STATUS_COMPLETE = 1;
    public static final int CONVERT_COMP_STATUS_TRANSLATING = 0;

    public static final int VERSION_0 = 0;
    public static final int VERSION_1 = 1;
    public static final int VERSION_2_SPEAKER = 2;

    public static final int SHOW_SWITH_TRUE = 1;
    public static final int SHOW_SWITH_FALSE = 0;

    public static final int SERVER_PLAN_XUNFEI = 3;
    public static final int SERVER_PLAN_BEIYAN = 1;

    /*非实时ASR*/
    public static final int SERVER_PLAN_ASR = 10;
    /*实时ASR*/
    public static final int SERVER_PLAN_ASR_REAL_TIME = 11;

    public static final int SPEAKER_ROLE_HAS_FIRSTSHOW_TRUE = 1;
    public static final int SPEAKER_ROLE_HAS_FIRSTSHOW_FALSE = 0;

    public static final int SPEAKER_ROLE_ISSHOWING_TRUE = 1;
    public static final int SPEAKER_ROLE_ISSHOWING_FALSE = 0;

    public static final int SPEAKER_ROLE_NUMBER_ONE = 1;
    public static final int SPEAKER_ROLE_NUMBER_FOUR = 4;

    public static synchronized boolean insertCheckRecordId(ConvertRecord record) {
        if (record == null) {
            DebugUtil.d(TAG, "insertCheckRecordId record can't be null!");
            return false;
        }
        ConvertRecord existRecord = selectByRecordId(record.getRecordId());
        if (existRecord != null) {
            if (existRecord.getId() == record.getId()) {
                updateConvertRecordWithUploadRecords(record);
            } else {
                deleteByRecordId(record.getRecordId());
                return insert(record);
            }
        } else {
            return insert(record);
        }
        return false;
    }


    public static boolean insert(ConvertRecord record) {
        if (record == null) {
            DebugUtil.d(TAG, "insert record can't be null!");
            return false;
        }
        try {
            ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
            Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
            ContentValues values = record.getContentValuesWithoutId();
            resolver.insert(uri, values);
            List<UploadRecord> uploadRecords = record.getUploadRecordList();
            boolean existRecordHasUploadRecord = false;
            if (uploadRecords != null) {
                existRecordHasUploadRecord = uploadRecords.size() > 0;
            }
            if (existRecordHasUploadRecord) {
                int insertCount = UploadDbUtil.insertUploadRecords(BaseApplication.getAppContext(), uploadRecords);
                DebugUtil.i(TAG, "insert inputCount: " + uploadRecords.size() + ", outputCount: " + insertCount);
            }
            return true;
        } catch (Exception e) {
            DebugUtil.e(TAG, "insert error " + e);
        }
       return false;
    }

    public static synchronized int deleteByRecordId(long recordId) {
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        ConvertRecord convertRecord = selectByRecordId(recordId);
        if (convertRecord != null) {
            String onlyId = convertRecord.getOnlyId();
            int deleteCount = UploadDbUtil.deleteByOnlyId(BaseApplication.getAppContext(), onlyId);
            DebugUtil.i(TAG, "deleteByRecordId, delete upload db records count: " + deleteCount);
        }
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        int deleteCount = resolver.delete(uri,
                DatabaseConstant.ConvertColumn.RECORD_ID + " = ? ",
                new String[]{String.valueOf(recordId)});
        DebugUtil.i(TAG, "deleteByRecordId " + recordId + ", delete count: " + deleteCount);
        return deleteCount;
    }

    public static synchronized int deleteByMediaPath(String mediaPath) {
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        int deleteCount = resolver.delete(uri,
                DatabaseConstant.ConvertColumn.MEDIA_PATH + " = ? ",
                new String[]{String.valueOf(mediaPath)});
        DebugUtil.i(TAG, "deleteByMediaPath " + mediaPath + ", delete count: " + deleteCount);
        return deleteCount;
    }

    public static synchronized int deleteByConvertTextFilePath(String convertTextFilePath) {
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        int deleteCount = resolver.delete(uri,
                DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH + " = ? ",
                new String[]{String.valueOf(convertTextFilePath)});
        DebugUtil.i(TAG, "deleteByConvertTextFilePath " + convertTextFilePath + ", delete count: " + deleteCount);
        return deleteCount;
    }

    public static synchronized long deleteConvertDataByConvertTextFilePrefix(String convertTextFilePathPrefix) {
        int deleteRowCount = 0;
        if (TextUtils.isEmpty(convertTextFilePathPrefix)) {
            DebugUtil.w(TAG, "deleteConvertDataByConvertTextFilePrefix convertTextFilePathPrefix is empty");
            return deleteRowCount;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        try {
            deleteRowCount = resolver.delete(uri,
                    DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH + " LIKE ?",
                    new String[]{convertTextFilePathPrefix + "%"});
        } catch (java.lang.Exception e) {
            DebugUtil.e(TAG, "deleteConvertDataByConvertTextFilePrefix error" + e.getMessage());
        }
        DebugUtil.d(TAG, "deleteConvertDataByConvertTextFilePrefix deleteRowCount=" + deleteRowCount);
        return deleteRowCount;
    }

    public static synchronized long deleteConvertDataByMediaPathPrefix(String mediaPathPrefix) {
        int deleteRowCount = 0;
        if (TextUtils.isEmpty(mediaPathPrefix)) {
            DebugUtil.w(TAG, "deleteConvertDataByMediaPathPrefix mediaPathPrefix is empty");
            return deleteRowCount;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        try {
            deleteRowCount = resolver.delete(uri,
                    DatabaseConstant.ConvertColumn.MEDIA_PATH + " LIKE ?",
                    new String[]{mediaPathPrefix + "%"});
        } catch (Exception e) {
            DebugUtil.e(TAG, "deleteConvertDataByMediaPathPrefix error" + e.getMessage());
        }
        DebugUtil.d(TAG, "deleteConvertDataByMediaPathPrefix deleteRowCount=" + deleteRowCount);
        return deleteRowCount;
    }

    public static synchronized int updateConvertRecordWithoutUploadRecords(ConvertRecord record) {
        if (record == null) {
            DebugUtil.d(TAG, "update record can't be null!");
            return 0;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        ContentValues values = record.getContentValues();
        try {
            return resolver.update(uri, values,
                    DatabaseConstant.ConvertColumn._ID + " = ? ",
                    new String[]{String.valueOf(record.getId())});
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateConvertRecordWithoutUploadRecords error" + e);
        }
        return 0;
    }


    public static synchronized int updateConvertRecordWithoutUploadRecordsOnRecordId(ConvertRecord record) {
        if (record == null) {
            DebugUtil.d(TAG, "update record can't be null!");
            return 0;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        ContentValues values = record.getContentValues();
        try {
            return resolver.update(uri, values,
                    DatabaseConstant.ConvertColumn.RECORD_ID + " = ? ",
                    new String[]{String.valueOf(record.getRecordId())});
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateConvertRecordWithoutUploadRecordsOnRecordId error " + e);
        }
        return 0;
    }


    public static synchronized int updateConvertStatusOnSendOCS(long recordId, int status) {
        return updateIntTarget(recordId, DatabaseConstant.ConvertColumn.UPLOAD_STATUS, status);
    }

    public static synchronized int updateConvertStatusOnConvert(long recordId, int status) {
        return updateIntTarget(recordId, DatabaseConstant.ConvertColumn.CONVERT_STATUS, status);
    }

    public static synchronized int updateKey(long recordId, String key) {
        return updateStringTarget(recordId, DatabaseConstant.ConvertColumn.UPLOAD_KEY, key);
    }

    public static synchronized int updateUploadId(long recordId, String uploadId) {
        return updateStringTarget(recordId, DatabaseConstant.ConvertColumn.UPLOAD_REQUEST_ID, uploadId);
    }

    public static synchronized int updatePartCount(long recordId, int partCount) {
        return updateIntTarget(recordId, DatabaseConstant.ConvertColumn.PART_COUNT, partCount);
    }

    public static synchronized int updateConvertTaskId(long recordId, String taskId) {
        return updateStringTarget(recordId, DatabaseConstant.ConvertColumn.TASKID, taskId);
    }

    public static synchronized int updateAllUrl(long recordId, String objectUrl) {
        return updateStringTarget(recordId, DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL, objectUrl);
    }

    public static synchronized int updateHistoryRoleName(long recordId, String historyRoleName) {
        return updateStringTarget(recordId, DatabaseConstant.ConvertColumn.HISTORY_ROLENAME, historyRoleName);
    }

    public static synchronized int updateCanShowSpeakerRole(long recordId, int canShowSpeakerRole) {
        return updateIntTarget(recordId, DatabaseConstant.ConvertColumn.CAN_SHOW_SPEAKER_ROLE, canShowSpeakerRole);
    }

    public static synchronized int updateServerPlanCode(long recordId, int serverPlanCode) {
        return updateIntTarget(recordId, DatabaseConstant.ConvertColumn.SERVER_PLAN_CODE, serverPlanCode);
    }

    public static synchronized int updateSpeakerRoleIsShowing(long recordId, int speakerRoleIsShowing) {
        return updateIntTarget(recordId, DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ISSHOWING, speakerRoleIsShowing);
    }

    public static synchronized int updateSpeakerRoleOriginalNumber(long recordId, int speakerRoleOriginalNumber) {
        return updateIntTarget(recordId, DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER, speakerRoleOriginalNumber);
    }

    public static synchronized int updateSpeakerRoleHasFirstshow(long recordId, int speakerRoleHasFirstshow) {
        return updateIntTarget(recordId, DatabaseConstant.ConvertColumn.SPEAKER_ROLE_HAS_FIRSTSHOW, speakerRoleHasFirstshow);
    }


    public static synchronized int updateStringTarget(long recordId, String targetName, String target) {
        DebugUtil.i(TAG, "targetName: " + targetName + " taget: " + target);
        ConvertRecord record = selectByRecordId(recordId);
        if (record == null) {
            DebugUtil.d(TAG, "update record can't be null!");
            return 0;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        ContentValues values = record.getContentValues();
        values.put(targetName, target);
        int updateCount = 0;
        try {
            updateCount = resolver.update(uri, values,
                    DatabaseConstant.ConvertColumn.RECORD_ID + " = ? ",
                    new String[]{String.valueOf(record.getRecordId())});
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateStringTarget error" + e);
        }
        DebugUtil.i(TAG, "updateConvertStatus recordId: " + recordId + "  updatecount: " + updateCount);
        return updateCount;
    }

    public static synchronized int updateIntTarget(long recordId, String targetName, int target) {
        DebugUtil.i(TAG, "targetName: " + targetName + " taget: " + target);
        ConvertRecord record = selectByRecordId(recordId);
        if (record == null) {
            DebugUtil.d(TAG, "update record can't be null!");
            return 0;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        ContentValues values = record.getContentValues();
        values.put(targetName, target);
        int updateCount = 0;
        try {
            updateCount = resolver.update(uri, values,
                    DatabaseConstant.ConvertColumn.RECORD_ID + " = ? ",
                    new String[]{String.valueOf(record.getRecordId())});
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateIntTarget error " + e);
        }
        DebugUtil.i(TAG, "updateConvertStatus recordId: " + recordId + "  updatecount: " + updateCount);
        return updateCount;
    }


    public static synchronized int updateConvertRecordWithUploadRecords(ConvertRecord record) {
        if (record == null) {
            DebugUtil.d(TAG, "update record can't be null!");
            return 0;
        }
        ConvertRecord convertRecord = selectByRecordId(record.getId());
        if (convertRecord != null) {
            String onlyId = convertRecord.getOnlyId();
            int deleteCount = UploadDbUtil.deleteByOnlyId(BaseApplication.getAppContext(), onlyId);
            DebugUtil.i(TAG, "updateConvertRecordWithUploadRecords delete uploadcount first delete count: " + deleteCount);
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        ContentValues values = record.getContentValues();
        int updateCount = 0;
        try {
            updateCount = resolver.update(uri, values,
                    DatabaseConstant.ConvertColumn._ID + " = ? ",
                    new String[]{String.valueOf(record.getId())});
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateConvertRecordWithUploadRecords error " + e);
        }
        UploadDbUtil.insertUploadRecords(BaseApplication.getAppContext(), record.getUploadRecordList());
        DebugUtil.i(TAG, "updateConvertRecordWithUploadRecords updatecount: " + updateCount);
        return updateCount;
    }


    public static synchronized @Nullable ConvertRecord selectByRecordId(long recordId) {
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        Cursor cursor = null;
        try {
            cursor = resolver.query(uri, ConvertRecord.getProjections(),
                    DatabaseConstant.ConvertColumn.RECORD_ID + " = ? ",
                    new String[]{String.valueOf(recordId)}, null);
        } catch (Exception e) {
            DebugUtil.e(TAG, ""+e);
        }
        List<ConvertRecord> results = getCursorDatas(cursor);
        if (results.isEmpty()) {
            return null;
        } else {
            DebugUtil.e(TAG, "different record has same record_id, check database! results.size="+results.size());
            return results.get(0);
        }
    }

    public static synchronized @Nullable
    ConvertRecord selectByMediaPath(String mediaPath) {
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        Cursor cursor = null;
        try {
            cursor = resolver.query(uri, ConvertRecord.getProjections(),
                    DatabaseConstant.ConvertColumn.MEDIA_PATH + " = ? ",
                    new String[]{mediaPath}, null);
        } catch (Exception e) {
            DebugUtil.e(TAG, "" + e);
        }
        List<ConvertRecord> results = getCursorDatas(cursor);
        if (results.size() == 0) {
            return null;
        } else {
            DebugUtil.e(TAG, "different record has same record_id, check database! results.size=" + results.size());
            return results.get(0);
        }
    }

    public static synchronized int updateRecordIdByMediaPath(String mediaPath, long mediaId) {
        if (TextUtils.isEmpty(mediaPath)) {
            DebugUtil.i(TAG, "update record can't be null!");
            return 0;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        String where = DatabaseConstant.ConvertColumn.MEDIA_PATH + " = ?";
        String[] whereArgs = new String[]{mediaPath};
        Cursor cursor = null;
        try {
            cursor = resolver.query(uri, ConvertRecord.getProjections(), where, whereArgs, null);
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateRecordIdByMediaPath error: " + e);
        }
        List<ConvertRecord> results = getCursorDatas(cursor);
        ContentValues cv = new ContentValues();
        cv.put(DatabaseConstant.ConvertColumn.RECORD_ID, mediaId);
        if (results.size() > 0) {
            StringBuilder sb = new StringBuilder();
            ArrayList<String> whereArgList = new ArrayList<>();
            for (ConvertRecord convertRecord : results) {
                boolean isNeedUpdate = (convertRecord.getRecordId() == -1);
                if (isNeedUpdate) {
                    if (sb.length() == 0) {
                        sb.append(DatabaseConstant.ConvertColumn._ID + " = ?");
                        whereArgList.add(String.valueOf(convertRecord.getId()));
                    } else {
                        sb.append(" or (");
                        sb.append(DatabaseConstant.ConvertColumn._ID + " = ?)");
                        whereArgList.add(String.valueOf(convertRecord.getId()));
                    }
                }
            }
            if (sb.length() > 0) {
                String whereId = sb.toString();
                String[] whereIdArgs = whereArgList.toArray(new String[whereArgList.size()]);
                DebugUtil.i(TAG, "updateRecordIdByMediaPath where: " + whereId + ", whereIdArgs: " + Arrays.asList(whereIdArgs));
                int updateCount = 0;
                try {
                    updateCount = resolver.update(uri, cv, whereId, whereIdArgs);
                } catch (Exception e) {
                    DebugUtil.e(TAG, "updateRecordIdByMediaPath: error: " + e);

                }
                DebugUtil.i(TAG, "updateRecordIdByMediaPath: updateCount: " + updateCount);
                return updateCount;
            }
        }
        return 0;
    }

    public static synchronized @Nullable
    ConvertRecord selectById(long id) {
        Cursor cursor = null;
        try {
            ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
            Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
             cursor = resolver.query(uri, ConvertRecord.getProjections(),
                    DatabaseConstant.ConvertColumn._ID + " = ? ",
                    new String[]{String.valueOf(id)}, null);
        } catch (Exception e) {
            DebugUtil.e(TAG, "selectById error: " + e);
        }
        List<ConvertRecord> results = getCursorDatas(cursor);
        if (results.size() != 1) {
            DebugUtil.e(TAG, "selectById: wrong results!");
            return null;
        }
        return results.get(0);
    }

    public static synchronized @Nullable
    List<ConvertRecord> selectByIds(List<Long> ids) {
        Cursor cursor = null;
        try {
            ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
            Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
            cursor = resolver.query(uri, ConvertRecord.getProjections(),
                    DatabaseConstant.ConvertColumn.RECORD_ID + " in(" + TextUtils.join(",", ids) + ") ",
                    null, null);
        } catch (Exception e) {
            DebugUtil.e(TAG, "selectByIds error: " + e);
        }
        return getCursorDatas(cursor);
    }

    public static synchronized @Nullable
    List<String> selectByConvertTextFilePathPrefix(String convertTextFilePathPrefix) {
        if (TextUtils.isEmpty(convertTextFilePathPrefix)) {
            DebugUtil.w(TAG, "selectByConvertTextFilePathPrefix pathPrefix is empty");
            return Collections.emptyList();
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        List<String> convertTextFilePaths = new ArrayList<>();
        Cursor cursor = null;
        try {
            cursor = resolver.query(DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI,
                    new String[]{DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH},
                    DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH + " LIKE ?",
                    new String[]{convertTextFilePathPrefix + "%"},
                    null);
            if (cursor != null && cursor.moveToFirst()) {
                do {
                    String path = cursor.getString(cursor.getColumnIndexOrThrow(DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH));
                    convertTextFilePaths.add(path);
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "selectByConvertTextFilePathPrefix Exception " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return convertTextFilePaths;
    }

    public static synchronized List<ConvertRecord> selectAll() {
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        Cursor cursor = null;
        try {
            cursor = resolver.query(uri, ConvertRecord.getProjections(), null, null);
        } catch (Throwable e) {
            DebugUtil.e(TAG, "selectAll with an Exception", e);
        }
        return getCursorDatas(cursor);
    }

    public static int queryConvertSuccessCount() {
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        Cursor cursor = null;
        int count = 0;
        try {
            cursor = resolver.query(uri, ConvertRecord.getProjections(), DatabaseConstant.ConvertColumn.COMPLETE_STATUS + " = ? ",
                    new String[]{String.valueOf(CONVERT_COMP_STATUS_COMPLETE)}, null);
            if (cursor != null) {
                count = cursor.getCount();
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "selectAll with an Exception", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }

    private static @NonNull
    List<ConvertRecord> getCursorDatas(Cursor cursor) {
        List<ConvertRecord> results = new ArrayList<>();
        try {
            if ((cursor != null) && (cursor.getCount() > 0)) {
                while (cursor.moveToNext()) {
                    ConvertRecord record = new ConvertRecord();
                    int index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn._ID);
                    if (index >= 0) {
                        record.setId(cursor.getLong(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.RECORD_ID);
                    if (index >= 0) {
                        record.setRecordId(cursor.getLong(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.MEDIA_PATH);
                    if (index >= 0) {
                        record.setMediaPath(cursor.getString(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH);
                    if (index >= 0) {
                        record.setConvertTextfilePath(cursor.getString(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.CHUNK_NAME);
                    if (index >= 0) {
                        record.setChunkName(cursor.getString(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.COMPLETE_STATUS);
                    if (index >= 0) {
                        record.setCompleteStatus(cursor.getInt(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.ONLY_ID);
                    String onlyId = "";
                    if (index >= 0) {
                        onlyId = cursor.getString(index);
                        record.setOnlyId(onlyId);
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.VERSION);
                    if (index >= 0) {
                        record.setVersion(cursor.getInt(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.TASKID);
                    if (index >= 0) {
                        record.setTaskId(cursor.getString(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.UPLOAD_REQUEST_ID);
                    if (index >= 0) {
                        record.setUploadRequestId(cursor.getString(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.UPLOAD_KEY);
                    if (index >= 0) {
                        record.setUploadKey(cursor.getString(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.PART_COUNT);
                    if (index >= 0) {
                        record.setPartCount(cursor.getInt(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.UPLOAD_STATUS);
                    if (index >= 0) {
                        record.setUploadStatus(cursor.getInt(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.CONVERT_STATUS);
                    if (index >= 0) {
                        record.setConvertStatus(cursor.getInt(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL);
                    if (index >= 0) {
                        record.setUploadAllUrl(cursor.getString(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.HISTORY_ROLENAME);
                    if (index >= 0) {
                        record.setHistoryRoleName(cursor.getString(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.SERVER_PLAN_CODE);
                    if (index >= 0) {
                        record.setServerPlanCode(cursor.getInt(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.CAN_SHOW_SPEAKER_ROLE);
                    if (index >= 0) {
                        record.setCanShowSpeakerRole(cursor.getInt(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ISSHOWING);
                    if (index >= 0) {
                        record.setSpeakerRoleIsShowing(cursor.getInt(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER);
                    if (index >= 0) {
                        record.setSpeakerRoleOriginalNumber(cursor.getInt(index));
                    }
                    index = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_HAS_FIRSTSHOW);
                    if (index >= 0) {
                        record.setSpeakerRoleHasFirstshow(cursor.getInt(index));
                    }
                    List<UploadRecord> uploadRecords = UploadDbUtil.getUploadRecordsByonlyId(BaseApplication.getAppContext(), onlyId);
                    record.setUploadRecordList(uploadRecords);
                    results.add(record);
                }
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "getCursorDatas error", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return results;
    }

    public static boolean checkAlreadyConvertComplete(long recordId) {
        ConvertRecord convertRecord = ConvertDbUtil.selectByRecordId(recordId);
        return checkAlreadyConvertComplete(convertRecord);
    }

    public static boolean checkAlreadyConvertComplete(ConvertRecord convertRecord) {
        if (convertRecord != null) {
            String txtFileFath = convertRecord.getConvertTextfilePath();
            int completeStatus = convertRecord.getCompleteStatus();
            boolean dbComplete = completeStatus == CONVERT_COMP_STATUS_COMPLETE;
            boolean textFileExist = false;
            if (!TextUtils.isEmpty(txtFileFath)) {
                File textFile = new File(txtFileFath);
                textFileExist = textFile.exists();
            }
            return textFileExist && dbComplete;
        }
        return false;
    }


    public static int getConvertProgressFromConvertRecord(ConvertRecord convertRecord) {
        int progress = 0;
        if (convertRecord != null) {
            String chunkName = convertRecord.getChunkName();
            if (!TextUtils.isEmpty(chunkName)) {
                long endTimeInMills = ChunkNameUtil.parseEndTimeFromChunkName(chunkName) / NumberConstant.NUM_1000;
                long duration = MediaDBUtils.getDurationFromMediaId(convertRecord.getRecordId());
                if (duration != 0L) {
                    progress = (int) (NumberConstant.NUM_100 * ((float) endTimeInMills / (float) duration));
                }
            }
        }
        return progress;
    }


    public static boolean updateAllUrlByRecordId(long mediaId, String allUrl) {
        if (mediaId == -1) {
            DebugUtil.i(TAG, "update record can't be null!");
            return false;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        String where = DatabaseConstant.ConvertColumn.RECORD_ID + " = ?";
        String[] whereArgs = new String[]{String.valueOf(mediaId)};
        ContentValues cv = new ContentValues();
        cv.put(DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL, allUrl);
        int updateCount = 0;
        try {
            updateCount = resolver.update(uri, cv, where, whereArgs);
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateAllUrlByRecordId error " + e);
        }
        DebugUtil.i(TAG, "updateAllUrlByRecordId where: " + where + ", whereIdArgs: " + Arrays.asList(whereArgs) + "updateCount:" + updateCount);
        return updateCount > 0;
    }


    public static boolean updateConvertStatusByRecordId(long mediaId, ConvertStatus convertStatus) {
        if (mediaId == -1) {
            DebugUtil.i(TAG, "update record can't be null!");
            return false;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        String where = DatabaseConstant.ConvertColumn.RECORD_ID + " = ?";
        String[] whereArgs = new String[]{String.valueOf(mediaId)};
        ContentValues cv = new ContentValues();
        cv.put(DatabaseConstant.ConvertColumn.UPLOAD_STATUS, convertStatus.getUploadStatus());
        cv.put(DatabaseConstant.ConvertColumn.CONVERT_STATUS, convertStatus.getConvertStatus());
        int updateCount = 0;
        try {
            updateCount = resolver.update(uri, cv, where, whereArgs);
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateConvertStatusByRecordId error " + e);
        }
        DebugUtil.i(TAG, "updateConvertStatusByRecordId where: " + where + ", whereIdArgs: " + Arrays.asList(whereArgs) + "updateCount:" + updateCount
                + ", new uploadStatus: " + convertStatus.getUploadStatus() + ", new convertStatus: " + convertStatus.getConvertStatus());
        return updateCount > 0;
    }


    public static boolean updateTaskIdByRecordId(long mediaId, String taskId) {
        if (mediaId == -1) {
            DebugUtil.i(TAG, "update record can't be null!");
            return false;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        String where = DatabaseConstant.ConvertColumn.RECORD_ID + " = ?";
        String[] whereArgs = new String[]{String.valueOf(mediaId)};
        ContentValues cv = new ContentValues();
        cv.put(DatabaseConstant.ConvertColumn.TASKID, taskId);
        int updateCount = 0;
        try {
            updateCount = resolver.update(uri, cv, where, whereArgs);
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateTaskIdByRecordId error " + e);
        }
        DebugUtil.i(TAG, "updateTaskIdByRecordId where: " + where + ", whereIdArgs: " + Arrays.asList(whereArgs) + "updateCount:" + updateCount);
        return updateCount > 0;
    }

    public static boolean updateTaskIdAndUploadAllUrl(long mediaId, String taskId, String allUrl) {
        if (mediaId == -1) {
            DebugUtil.i(TAG, "update record can't be null!");
            return false;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        String where = DatabaseConstant.ConvertColumn.RECORD_ID + " = ?";
        String[] whereArgs = new String[]{String.valueOf(mediaId)};
        ContentValues cv = new ContentValues();
        cv.put(DatabaseConstant.ConvertColumn.TASKID, taskId);
        cv.put(DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL, allUrl);
        int updateCount = 0;
        try {
            updateCount = resolver.update(uri, cv, where, whereArgs);
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateTaskIdAndUploadAllUrl error " + e);
        }
        DebugUtil.i(TAG, "updateTaskIdByRecordId where: " + where + ", whereIdArgs: " + Arrays.asList(whereArgs) + "updateCount:" + updateCount);
        return updateCount > 0;
    }


    public static boolean updateCompleteAndFilePathByRecordId(long mediaId, String convertFilePath) {
        if (mediaId == -1) {
            DebugUtil.i(TAG, "update record can't be null!");
            return false;
        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;
        String where = DatabaseConstant.ConvertColumn.RECORD_ID + " = ?";
        String[] whereArgs = new String[]{String.valueOf(mediaId)};
        ContentValues cv = new ContentValues();
        cv.put(DatabaseConstant.ConvertColumn.COMPLETE_STATUS, CONVERT_COMP_STATUS_COMPLETE);
        cv.put(DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH, convertFilePath);
        int updateCount = 0;
        try {
            updateCount = resolver.update(uri, cv, where, whereArgs);
        } catch (Exception e) {
            DebugUtil.e(TAG, "updateCompleteAndFilePathByRecordId error " + e);
        }
        DebugUtil.i(TAG, "updateCompleteAndFilePathByRecordId where: " + where + ", whereIdArgs: " + Arrays.asList(whereArgs) + "updateCount:" + updateCount);
        return updateCount > 0;
    }

    public static synchronized int update(ConvertRecord record) {

        if (record == null) {
            DebugUtil.d(TAG, "update record can't be null!");
            return 0;

        }
        ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();

        Uri uri = DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI;

        ContentValues values = record.getContentValues();
        try {
            return resolver.update(uri, values,
                    DatabaseConstant.ConvertColumn._ID + " = ? ",
                    new String[]{String.valueOf(record.getId())});
        } catch (Exception e) {
            DebugUtil.e(TAG, "update error " + e);
        }
        return 0;
    }


    public static void setConvertServicePlanCode(int code) {
        StorageManager.setIntPref(BaseApplication.getAppContext(), "convert_service_plan_code_key", code);
    }

    public static int getConvertServicePlanCode() {
        return StorageManager.getIntPref(BaseApplication.getAppContext(), "convert_service_plan_code_key", SERVER_PLAN_XUNFEI);
    }
}
