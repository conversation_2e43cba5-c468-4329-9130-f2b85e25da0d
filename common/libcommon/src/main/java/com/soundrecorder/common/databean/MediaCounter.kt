/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description : 媒体库查询统计，文件计数器
 * * Version     : 1.0
 * * Date        : 2025/04/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.databean

class MediaCounter {
    var allCount: Int = 0
    var standardCount: Int = 0
    var meetingCount: Int = 0
    var interviewCount: Int = 0
    var callCount: Int = 0
    var recycleCount: Int = 0
    var commonCount: Int = 0
    var callerNameCount: Int = 0

    fun addAllCount() {
        allCount++
    }

    fun addStandardCount() {
        standardCount++
    }

    fun addMeetingCount() {
        meetingCount++
    }

    fun addInterviewCount() {
        interviewCount++
    }

    fun addCallCount() {
        callCount++
    }

    fun addRecycleCount() {
        recycleCount++
    }

    fun addCommonCount() {
        commonCount++
    }

    fun addCallerNameCount() {
        callerNameCount++
    }

    fun release() {
        allCount = 0
        standardCount = 0
        meetingCount = 0
        interviewCount = 0
        callCount = 0
        recycleCount = 0
        commonCount = 0
        callerNameCount = 0
    }
}
