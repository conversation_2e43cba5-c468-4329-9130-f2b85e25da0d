/************************************************************
 * Copyright 2000-2009 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : DeleteSoundEffectManager.java
 * Version Number: 1.0
 * Description   :
 * Author        : huangyuanwang
 * Date          : 2019-07-25
 * History       :(ID,  2019-07-25, huangyuanwang, delete item play sound , this is the manager who )
 ************************************************************/
package com.soundrecorder.common.utils.sound;

import android.content.Context;
import android.media.AudioManager;
import android.provider.Settings;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;

public class DeleteSoundEffectManager implements IDeleteSoundPlayer {

    static final String DELETE_PATH = "/system/media/audio/ui/global_delete.ogg";
    static final String DELETE_SOUND_FILENAME = "global_delete.ogg";

    private static final String TAG = "DeleteSoundEffectManager";
    private static final String SUPPORT_KEY = "global_delete_sound";
    private static final int DELETE_SOUND_CLOSE = 0;
    private static final int DELETE_SOUND_OPEN = 1;

    private static DeleteSoundEffectManager sInstance;
    private IDeleteSoundPlayer mDeleteSoundPlayer;

    public static synchronized DeleteSoundEffectManager getInstance() {
        if (sInstance == null) {
            sInstance = new DeleteSoundEffectManager();
        }
        return sInstance;
    }

    private DeleteSoundEffectManager() {
    }


    public void initData() {
        if (mDeleteSoundPlayer == null) {
            if (BaseUtil.isAndroidROrLater()) {
                mDeleteSoundPlayer = new DeleteSoundMediaPlayer();
            } else {
                mDeleteSoundPlayer = new DeleteSoundPoolPlayer();
            }
        }
    }

    public void playDeleteSound() {
        if (!isDeleteSoundSwitchOpen()) {
            DebugUtil.i(TAG, "playDeleteSound switch is close");
            return;
        }
        if (judgeNotToPlay(BaseApplication.getAppContext())) {
            DebugUtil.i(TAG, "judgeNotToPlay return, not play deleteSound");
            return;
        }
        initData();
        if (mDeleteSoundPlayer != null) {
            mDeleteSoundPlayer.playDeleteSound();
        }
    }


    public void release() {
        if (mDeleteSoundPlayer != null) {
            mDeleteSoundPlayer.release();
            DebugUtil.i(TAG, "mDeleteSoundPlayer release");
        }
        resetData();
    }

    private void resetData() {
        synchronized (DeleteSoundEffectManager.class) {
            sInstance = null;
        }
    }


    private boolean isDeleteSoundSwitchOpen() {
        Context context = BaseApplication.getAppContext();
        int switchInt = Settings.Secure.getInt(context.getContentResolver(), SUPPORT_KEY, DELETE_SOUND_OPEN);
        boolean isDeleteSoundOpen = (switchInt != DELETE_SOUND_CLOSE);
        DebugUtil.i(TAG, "isDeleteSoundSwitchOpen: switchInt: " + switchInt);
        return isDeleteSoundOpen;
    }


    private boolean judgeNotToPlay(Context context) {
        AudioManager mAudioManager = null;
        try {
            mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            if (mAudioManager != null) {
                int currentMode = mAudioManager.getRingerMode();
                DebugUtil.i(TAG, "judgeNotToPlay currentMode: " + currentMode);
                return (currentMode == AudioManager.RINGER_MODE_SILENT) || (currentMode == AudioManager.RINGER_MODE_VIBRATE);
            } else {
                return false;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "justToPlay AudioManager get Failed", e);
            return false;
        }
    }

}
