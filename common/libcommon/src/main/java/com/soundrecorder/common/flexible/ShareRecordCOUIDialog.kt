/*
Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
File: - ShareSummaryAndTextPreference
Description: 录音分享录音弹窗工具类
Version: 1.0
Date : 2025/6/3
Author: W9067780
--------------------- Revision History: ---------------------
<author>	<data> 	  <version >	   <desc>
W9067780  2025/6/3     1.0      create this file
*/
package com.soundrecorder.common.flexible

import android.content.DialogInterface
import androidx.fragment.app.FragmentManager
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.soundrecorder.base.utils.DebugUtil

class ShareRecordCOUIDialog(val childFragmentManager: FragmentManager?) {
    companion object {
        private const val TAG = "ShareRecordCOUIDialog"
    }

    private var bottomSheetDialogFragment: COUIBottomSheetDialogFragment? = null

    fun showShareRecordDialog(callBack: (type: Int) -> Unit) {
        if (isShowing()) {
            DebugUtil.i(TAG, "bottomSheetDialogFragment is showing")
            return
        }
        bottomSheetDialogFragment = COUIBottomSheetDialogFragment()
        val shareRecordDialogFragment = ShareRecordDialogFragment()
        bottomSheetDialogFragment?.setMainPanelFragment(shareRecordDialogFragment)
        childFragmentManager?.let {
            bottomSheetDialogFragment?.show(
                it,
                ShareRecordDialogFragment.SHARE_TAG
            )
        }
        shareRecordDialogFragment.setOnShareRecordDialogClickEvent { type ->
            callBack.invoke(type)
        }
    }

    fun isShowing(): Boolean {
        return bottomSheetDialogFragment?.dialog?.isShowing == true
    }

    fun dismiss() {
        if (bottomSheetDialogFragment?.dialog?.isShowing == true) {
            bottomSheetDialogFragment?.dismiss()
        }
    }

    fun setOnDismissListener(listener: DialogInterface.OnDismissListener) {
        DebugUtil.i(TAG, "bottomSheetDialogFragment setOnDismissListener")
        bottomSheetDialogFragment?.setOnDismissListener {
            listener
        }
    }
}