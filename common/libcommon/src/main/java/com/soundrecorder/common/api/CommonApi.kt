/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CommonApi
 * Description:
 * Version: 1.0
 * Date: 2023/9/20
 * Author: W9020254
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9020254 2023/9/20 1.0 create
 */

package com.soundrecorder.common.api

import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.HoverStaticsUtil
import com.soundrecorder.modulerouter.CommonAction

object CommonApi : CommonAction {

    override fun hoverBuryingPoint(name: String) {
        HoverStaticsUtil.addHoverNumEvent(name)
    }

    override fun useRecordDurationMessage(time: Long) {
        BuryingPoint.addRecordDurationMessage(time)
    }

    override fun useRecordEntryLaunch(entryType: String, landingType: Int) {
        BuryingPoint.addRecordEntryLaunch(entryType, landingType)
    }
}