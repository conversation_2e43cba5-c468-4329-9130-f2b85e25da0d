/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RemovableAppManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/12/30 14:52
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/12/30       1.0      create
 ***********************************************************************/
package com.soundrecorder.common.removableapp

import android.content.Context
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.R
import com.soundrecorder.base.utils.AppUtil.isAppInstalled
import com.soundrecorder.base.utils.DebugUtil

/**
 * Removable App Manager.
 */
class RemovableAppManager(private val packageName: String) {

    private var mRemovableAppController = RemovableAppController(BaseApplication.getAppContext())
    private var mRemovableAppProviderController = RemovableAppProviderController(BaseApplication.getAppContext())

    /**
     * Obtain [packageName] removable app info.
     */
    fun obtainRemovableInfo(): RemovableAppInfo {
        return mRemovableAppProviderController.queryAppInfo(packageName)
    }

    /**
     * Re-install FileManager App.
     */
    fun reInstallApp(
        context: Context,
        removableAppInfo: RemovableAppInfo,
        callback: InstallResultCallback,
        negative: (() -> Unit?)?
    ) {
        mRemovableAppController.open()
        mRemovableAppController.registerCallback(object : RemovableAppStateCallback {
            override fun onOpen() {
                val isAppInstalled = isAppInstalled(packageName)
                DebugUtil.d(TAG, "restoreApp -> onOpen installed = $isAppInstalled")
                if (!isAppInstalled) {
                    internalInstallApp(context, removableAppInfo, callback)
                } else {
                    negative?.invoke()
                }
            }

            override fun onClose() {
                DebugUtil.d(TAG, "restoreApp -> onClose")
            }
        })
    }

    private fun internalInstallApp(
        context: Context,
        removableAppInfo: RemovableAppInfo,
        callback: InstallResultCallback
    ) {
        val installingDialog = showInstallingDialog(context)
        mRemovableAppController.restoreApp(removableAppInfo.packageName) { success ->
            DebugUtil.d(TAG, "internalInstallApp -> restore result is $success")
            installingDialog.dismiss()
            callback.onInstalledResult(success)
        }
    }

    private fun showInstallingDialog(context: Context): AlertDialog {
        val installingDialog = COUIRotatingDialogBuilder(context, context.getString(R.string.install))
            .show().apply {
                setCanceledOnTouchOutside(false)
            }
        return installingDialog
    }

    companion object {
        private const val TAG = "RemovableAppManager"
    }
}