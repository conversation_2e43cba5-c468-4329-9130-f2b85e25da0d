package com.soundrecorder.common.utils;

import android.content.Context;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextUtils;
import android.widget.EditText;

import com.soundrecorder.base.utils.RecorderTextUtils;
import com.soundrecorder.base.utils.DebugUtil;

import java.util.LinkedList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SubRecorderTextUtils extends RecorderTextUtils {
    private static final String TAG = "SubColorTextUtils";
    private static boolean sContainEmoji = false;
    private static boolean sContainIllgalFileName = false;
    private static Pattern sFileNameIllegalPattern;
    private static Pattern sEmojiPattern;
    private static final String BASE_EMOJI = "⭕|⭐|[☀-⟿]|[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\uD83E\uDD00-\uD83E\uDFFF]|[\u2600-\u27ff]|[\ue000-\uf8ff]";
    private static final String PLUGIN_EMOJI = "|[\u2190-\u2199]|[\u25a0-\u25a1]|[\u25b2-\u25b3]|[\u25bc-\u25bd]|[\u25c6-\u25c7]|[\u25ce-\u25cf]"
            + "|[\u25e2-\u25e5]|\\u203b|\\u25cb|\\u534d|\\u2121|\\u2299|\\u32a3|\\u2122|\\u3231|\\u56cd|\\u00ae|\\u00a9|\\u21aa|\\u21a3|\\u21d4";

    private static LinkedList<String> sSpecailEmoji = new LinkedList<String>();
    private static String sEmojiString;


    static {
        sSpecailEmoji.add("\\ufe0f");

        sSpecailEmoji.add("\\u25fd");
        sSpecailEmoji.add("\\u25fe");
        sSpecailEmoji.add("\\u2b1b");
        sSpecailEmoji.add("\\u2b1c");

        sSpecailEmoji.add("\\u23e9");
        sSpecailEmoji.add("\\u23ea");
        sSpecailEmoji.add("\\u23eb");
        sSpecailEmoji.add("\\u23ec");

        sSpecailEmoji.add("\\u231b");
        sSpecailEmoji.add("\\u23f3");
        sSpecailEmoji.add("\\u23f9");
        sSpecailEmoji.add("\\u23f8");
        sSpecailEmoji.add("\\u23fa");
        sSpecailEmoji.add("\\u231a");
        sSpecailEmoji.add("\\u23f0");

        sSpecailEmoji.add("\\u20e3");
    }

    static {
        StringBuffer emojiStringBuffer = new StringBuffer(BASE_EMOJI);
        emojiStringBuffer.append("|");
        for (int i = 0; i < sSpecailEmoji.size(); i++) {
            emojiStringBuffer.append(sSpecailEmoji.get(i));
            if (i < (sSpecailEmoji.size() - 1)) {
                emojiStringBuffer.append("|");
            }
        }
        emojiStringBuffer.append(PLUGIN_EMOJI);
        sEmojiString = emojiStringBuffer.toString();
        DebugUtil.i(TAG, "sEmojiString: " + sEmojiString);
    }

    public static void addEmojiInputFilter(EditText editText) {
        addInputFilter(editText, new InputFilter() {
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (containsEmoji(source)) {
                    setContainEmoji(true);
                    if (dest != null) {
                        return dest.subSequence(dstart, dend);
                    }
                } /*else {
                    if (!TextUtils.isEmpty(source)) {
                        Log.i(TAG,"inut source is " + string2Unicode(source) + ", original string is " + source + ",source length :" + source.length());
                    }
                }*/
                return source;
            }
        });
    }


    public static String string2Unicode(CharSequence string) {
        StringBuffer unicode = new StringBuffer();
        for (int i = 0; i < string.length(); i++) {
            // get everyChar
            char c = string.charAt(i);
            // convert to unicode
            unicode.append("\\u" + Integer.toHexString(c));
        }
        return unicode.toString();
    }

    public static void addIllgalFileNameInputFilter(EditText editText) {
        addInputFilter(editText, new InputFilter() {
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                if (SubRecorderTextUtils.containsIllegalCharFileName(source)) {
                    setContainIllgalFileName(true);
                    if (dest != null) {
                        return dest.subSequence(dstart, dend);
                    }
                }
                return source;
            }
        });
    }

    public static boolean containsIllegalCharFileName(CharSequence source) {
        if (TextUtils.isEmpty(source)) {
            return false;
        } else {
            if (sFileNameIllegalPattern == null) {
                sFileNameIllegalPattern = Pattern.compile(".*[\\\\/*:?？<>|\"]+?.*");
            }
            Matcher matcher = sFileNameIllegalPattern.matcher(source);
            return matcher.find();
        }
    }


    public static boolean containsEmoji(CharSequence source) {
        if (TextUtils.isEmpty(source)) {
            return false;
        } else {
            if (sEmojiPattern == null) {
                sEmojiPattern = Pattern.compile(sEmojiString, Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
            }
            Matcher matcher = sEmojiPattern.matcher(source);
            return matcher.find();
        }
    }

    public static boolean isContainEmoji() {
        return sContainEmoji;
    }

    public static void setContainEmoji(boolean sContainEmoji) {
        SubRecorderTextUtils.sContainEmoji = sContainEmoji;
    }

    public static boolean isContainIllgalFileName() {
        return sContainIllgalFileName;
    }

    public static void setContainIllgalFileName(boolean sContainIllgalFileName) {
        SubRecorderTextUtils.sContainIllgalFileName = sContainIllgalFileName;
    }
}
