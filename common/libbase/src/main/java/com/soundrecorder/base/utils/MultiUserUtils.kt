package com.soundrecorder.base.utils

import android.os.Process

/**
 * 多用户的工具类
 */
object MultiUserUtils {

    private const val TAG = "MultiUserUtils"

    /**
     * 系统分身是特殊的多用户
     * 判断是否是系统分身
     */
    @JvmStatic
    fun isSystemClone(): Boolean {
        kotlin.runCatching {
            val curUserHandle = Process.myUserHandle()
            val isMultiSys = AddonAdapterCompatUtil.isMultiSystemUserHandle(curUserHandle)
            DebugUtil.e(
                TAG,
                "isSystemClone currentUserHandle:$curUserHandle isMultiSys:$isMultiSys"
            )
            return isMultiSys
        }.onFailure {
            DebugUtil.e(TAG, "isSystemClone error", it)
        }
        return false
    }
}