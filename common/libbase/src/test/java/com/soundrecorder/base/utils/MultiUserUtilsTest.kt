/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MultiUserUtilsTest
 * Description:
 * Version: 1.0
 * Date: 2023/10/19
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/10/19 1.0 create
 */

package com.soundrecorder.base.utils

import android.os.Build
import android.os.Process
import android.os.UserHandle
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowLog::class, ShadowFeatureOption::class])
class MultiUserUtilsTest {

    @Test
    fun should_returnFalse_when_isSystemClone() {
        val mockAddonUtil = Mockito.mockStatic(AddonAdapterCompatUtil::class.java)
        val mockUserHandle = Mockito.mock(UserHandle::class.java)
        val mockProcess = Mockito.mockStatic(Process::class.java)
        mockProcess.`when`<UserHandle> { Process.myUserHandle() }.thenReturn(mockUserHandle)
        mockAddonUtil.`when`<Boolean> {
            AddonAdapterCompatUtil.isMultiSystemUserHandle(mockUserHandle)
        }
            .thenReturn(false, true)

        Assert.assertFalse(MultiUserUtils.isSystemClone())
        Assert.assertTrue(MultiUserUtils.isSystemClone())

        mockAddonUtil.`when`<Boolean> {
            AddonAdapterCompatUtil.isMultiSystemUserHandle(mockUserHandle)
        }
            .thenThrow(RuntimeException())
        Assert.assertFalse(MultiUserUtils.isSystemClone())

        mockAddonUtil.close()
        mockProcess.close()
    }
}