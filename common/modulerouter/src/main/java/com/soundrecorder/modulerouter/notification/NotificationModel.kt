/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NotificationModel
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.notification

import androidx.lifecycle.LiveData

class NotificationModel {
    companion object {
        const val RECORD_STATUS_PAUSE = 0
        const val RECORD_STATUS_PLAYING = 1
    }

    var playDuration: Long = 0
    var playName: LiveData<String>? = null
    var playStatus: LiveData<Int>? = null
    var curTime: LiveData<Long>? = null
    var isBtnDisabled: LiveData<Boolean>? = null
    var isMarkEnabled: LiveData<Boolean>? = null
    var markPoint:  LiveData<Long>? = null
    var canJumpIntent: Boolean = true
    var saveState: LiveData<Int>? = null
    var isRecycle: Boolean = false
}