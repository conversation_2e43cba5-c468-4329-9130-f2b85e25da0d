LOCAL_PATH:= $(call my-dir)

SINGLE_TARGET := NewSoundRecorder.apk

include $(CLEAR_VARS)
LOCAL_MODULE       := $(SINGLE_TARGET)
LOCAL_MODULE_TAGS  := optional
LOCAL_MODULE_CLASS := APPS
# ifeq ($(TARGET_ARCH),arm64)
# LOCAL_PREBUILT_JNI_LIBS := $(shell aapt l ${LOCAL_PATH}/$(SINGLE_TARGET) | grep lib/arm64.*/.*so | sort | sed 's/^/@/' | xargs)
# LOCAL_MULTILIB := 64
# else
# LOCAL_PREBUILT_JNI_LIBS := $(shell aapt l ${LOCAL_PATH}/$(SINGLE_TARGET) | grep lib/armeabi.*/.*so | sort | sed 's/^/@/' | xargs)
# endif
#system/app
LOCAL_SYS_OPPOAPP := true
ifneq ($(TARGET_BUILD_VERSION),US)
LOCAL_MODULE_PATH  :=$(TARGET_OUT_DATA_APPS)
else
LOCAL_MODULE_PATH  :=$(TARGET_OUT)/priv-app
endif
ifneq (1,$(filter 1,$(shell echo "$$(( $(PLATFORM_SDK_VERSION) <= 22 ))" )))
#6.0
ifeq (true, $(CHANGE_SIGN_NAME))
LOCAL_CERTIFICATE  := oppo_data_app_std
LOCAL_OPPO_OLD_CERTIFICATE := media
else
LOCAL_CERTIFICATE  := platform
endif
else
#5.1
LOCAL_CERTIFICATE  := PRESIGNED
endif
LOCAL_SRC_FILES    := $(SINGLE_TARGET)            
include $(BUILD_PREBUILT)
