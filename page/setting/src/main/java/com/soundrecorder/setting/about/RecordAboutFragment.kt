/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecordAboutFragment
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.setting.about

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.preference.Preference
import androidx.preference.Preference.OnPreferenceClickListener
import com.coui.appcompat.aboutpage.COUIAppInfoPreference
import com.coui.appcompat.preference.COUIJumpPreference
import com.coui.appcompat.toolbar.COUIToolbar
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.RtlUtils
import com.soundrecorder.base.view.RootViewPersistentInsetsCallback
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.common.widget.AutoIndentPreferenceFragment
import com.soundrecorder.setting.R
import com.soundrecorder.setting.SettingApi
import com.soundrecorder.setting.opensource.OpenSourceActivity

class RecordAboutFragment : AutoIndentPreferenceFragment(), OnPreferenceClickListener {
    private val RECORD_POLICY: String = "pref_record_policy"
    private val RECORD_LICENSE: String = "pref_record_license"
    private val keyRecordAppVersion: String = "pref_app_info"

    private var mRecordAbout: COUIJumpPreference? = null
    private var mRecordLicense: COUIJumpPreference? = null
    private var recordAppVersion: COUIAppInfoPreference? = null
    private var disableDialog: AlertDialog? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        initActionBar(view)
        listView?.isForceDarkAllowed = false
        addPreferencesFromResource(R.xml.record_about)
        mRecordAbout = findPreference<Preference>(RECORD_POLICY) as COUIJumpPreference?
        mRecordLicense = findPreference<Preference>(RECORD_LICENSE) as COUIJumpPreference?
        recordAppVersion = findPreference<Preference>(keyRecordAppVersion) as? COUIAppInfoPreference
        mRecordAbout?.onPreferenceClickListener = this
        mRecordLicense?.onPreferenceClickListener = this
        mRecordAbout?.isVisible = BaseUtil.isEXP()
        recordAppVersion?.let {
            if (BaseApplication.sIsRTLanguage) {
                val version = RtlUtils.LTR_START + AppUtil.getAppVersionName()
                it.appVersion =
                    resources.getString(com.soundrecorder.common.R.string.record_version, version)
            } else {
                it.appVersion =
                    resources.getString(
                        com.soundrecorder.common.R.string.record_version,
                        AppUtil.getAppVersionName()
                    )
            }
        }
        return view
    }

    private fun initActionBar(view: View?) {
        if (view == null) {
            return
        }
        val activity = activity as? AppCompatActivity? ?: return
        val toolbar = view.findViewById<COUIToolbar>(R.id.toolbar)
        activity.setSupportActionBar(toolbar)
        val actionBar = activity.supportActionBar
        actionBar?.setHomeButtonEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)
        actionBar?.setTitle(com.soundrecorder.common.R.string.record_about_new)
        initiateWindowInsets(activity)
    }

    private fun initiateWindowInsets(activity: Activity) {
        WindowCompat.setDecorFitsSystemWindows(activity.window, false)
        activity.findViewById<View>(R.id.root_layout)?.let {
            val callback: RootViewPersistentInsetsCallback = object : RootViewPersistentInsetsCallback() {
                override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                    super.onApplyInsets(v, insets)
                    DebugUtil.i(TAG, "onApplyInsets")
                    val navigationHeight = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars()).bottom
                    (activity as? RecordAboutActivity)?.onApplyInsets(navigationHeight)
                    TaskBarUtil.setNavigationColorOnSupportTaskBar(
                        navigationHeight = navigationHeight,
                        activity = activity,
                        defaultNoTaskBarColor = (activity as? BaseActivity)?.navigationBarColor()
                    )
                }
            }
            ViewCompat.setOnApplyWindowInsetsListener(it, callback)
        }
    }

    override fun onPreferenceClick(preference: Preference?): Boolean {
        when (preference?.key) {
            RECORD_POLICY -> {
                if (!ClickUtils.isQuickClick()) {
                    SettingApi.launchBootRegPrivacy(activity) {
                        disableDialog = it
                    }
                }
            }
            RECORD_LICENSE -> {
                startActivity(Intent(context, OpenSourceActivity::class.java))
            }
        }
        return false
    }

    override fun onDestroyView() {
        super.onDestroyView()
        disableDialog.dismissWhenShowing()
        disableDialog = null
    }
}