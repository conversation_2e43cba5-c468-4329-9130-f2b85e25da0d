package com.soundrecorder.browsefile.search.load.center.filechange

import android.os.*
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.search.load.center.CenterDbUtils
import com.soundrecorder.browsefile.shadows.ShadowCenterDbUtils
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowRecorderLogger
import org.junit.*
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowCenterDbUtils::class, ShadowFeatureOption::class,
        ShadowRecorderLogger::class, ShadowRecorderLogger::class]
)
class CenterFileChangeObserverTest {
    val METHOD_CHECK_EVENT_CAN_CALL_DMP = "checkEventCanCallDmp"

    val FIELD_HANDLER_THREAD = "mHandlerThread"
    val FIELD_WORK_HANDLER = "mDmpWorkHandler"

    val TEST_FILE_PATH_1 = "/Music/Recordings/Standard Recordings/1.mp3"
    val TEST_FILE_PATH_2 = "/Music/Recordings/Standard Recordings/2.mp3"
    val TEST_FILE_PATH_3 = "/Music/Recordings/Standard Recordings/3.mp3"


    @Before
    fun setup() {


    }

    @After
    fun release() {
        CenterFileChangeObserver.release()
    }


    @Test
    fun should_clearLocalData_when_initDmpSearch() {
        val mockedCenterDbUtils = Mockito.mockStatic(CenterDbUtils::class.java)
        Mockito.`when`(CenterDbUtils.isCenterSearchUsable()).thenReturn(false)
        CenterFileChangeObserver.initDmpSearch()

        val mIsFirstEnter =
            Whitebox.getInternalState<Boolean>(CenterFileChangeObserver.javaClass, "mIsFirstEnter")
        Assert.assertEquals(false, mIsFirstEnter)
        mockedCenterDbUtils.close()
    }

    @Test
    @Ignore
    fun should_correct_when_triggerRecorderFullCompare() {
        CenterFileChangeObserver.triggerRecorderFullCompare(true)
        val workHandler =
            Whitebox.getInternalState<DmpWorkHandler>(
                CenterFileChangeObserver.javaClass,
                FIELD_WORK_HANDLER
            )
        Assert.assertNotNull(workHandler)
        Assert.assertTrue(workHandler.hasMessages(DmpWorkHandler.MSG_WHAT_RECORDER_FULL_COMPARE))
    }

    @Test
    fun shoudl_when_checkUnSyncDirtyUpdateData() {
        CenterFileChangeObserver.checkUnSyncDirtyUpdateData()
        val workHandler =
            Whitebox.getInternalState<DmpWorkHandler?>(
                CenterFileChangeObserver.javaClass,
                FIELD_WORK_HANDLER
            )
        Assert.assertNull(workHandler)
    }


    @Test
    @Ignore
    fun should_updateLocalData_when_fileUpdateChangeSuccess() {
        val newPath = "/Music/Recordings/Standard Recordings/new1.mp3"
        CenterFileChangeObserver.fileUpdateChangeSuccess(1, newPath)
        val workHandler =
            Whitebox.getInternalState<DmpWorkHandler>(
                CenterFileChangeObserver.javaClass,
                FIELD_WORK_HANDLER
            )
        Assert.assertNotNull(workHandler)
        Assert.assertTrue(workHandler.hasMessages(DmpWorkHandler.MSG_WHAT_SYNC_DMP))
    }

    @Test
    fun should_init_when_initDmpWorkHander() {
        Whitebox.invokeMethod<Unit>(CenterFileChangeObserver, "initDmpWorkHander")
        val workHandler =
            Whitebox.getInternalState<DmpWorkHandler?>(
                CenterFileChangeObserver.javaClass,
                FIELD_WORK_HANDLER
            )
        val handlerThread =
            Whitebox.getInternalState<HandlerThread?>(
                CenterFileChangeObserver.javaClass,
                FIELD_HANDLER_THREAD
            )
        Assert.assertNotNull(handlerThread)
        Assert.assertNotNull(workHandler)
        Assert.assertEquals(handlerThread.looper, workHandler.looper)
        Assert.assertNotSame(workHandler.looper, Looper.getMainLooper())
    }


    @Test
    fun should_correct_when_checkEventCanCallDmp() {
        val supportEvent = arrayOf(
            FileObserver.MOVED_FROM,
            FileObserver.DELETE,
            FileObserver.MOVE_SELF,
            FileObserver.DELETE_SELF,
            FileObserver.CREATE,
            FileObserver.MOVED_TO,
            FileObserver.MODIFY
        )
        supportEvent.forEach {
            val result = Whitebox.invokeMethod<Boolean>(
                CenterFileChangeObserver,
                METHOD_CHECK_EVENT_CAN_CALL_DMP,
                it,
                TEST_FILE_PATH_1
            )
            Assert.assertTrue(result)
        }
    }

    @Test
    @Ignore
    fun should_sendMsgToHandler_when_onFileChange() {
        CenterFileChangeObserver.onFileChange(FileObserver.MOVED_FROM, "1.mp3", TEST_FILE_PATH_1)
        val workHandler =
            Whitebox.getInternalState<DmpWorkHandler>(
                CenterFileChangeObserver.javaClass,
                FIELD_WORK_HANDLER
            )
        Assert.assertTrue(workHandler.hasMessages(DmpWorkHandler.MSG_WHAT_FILE_DELETE))

        CenterFileChangeObserver.onFileChange(FileObserver.MOVED_TO, "1.mp3", TEST_FILE_PATH_1)
        Assert.assertTrue(workHandler.hasMessages(DmpWorkHandler.MSG_WHAT_FILE_UPDATE))
    }


    @Test
    fun should_release_when_release() {
        CenterFileChangeObserver.release()
        val handlerThread =
            Whitebox.getInternalState<HandlerThread>(
                CenterFileChangeObserver.javaClass,
                FIELD_HANDLER_THREAD
            )
        val workHandler =
            Whitebox.getInternalState<HandlerThread>(
                CenterFileChangeObserver.javaClass,
                FIELD_WORK_HANDLER
            )
        val mIsFirstEnter =
            Whitebox.getInternalState<Boolean>(CenterFileChangeObserver.javaClass, "mIsFirstEnter")

        Assert.assertNull(handlerThread)
        Assert.assertNull(workHandler)
        Assert.assertTrue(mIsFirstEnter)
    }

}