package com.soundrecorder.browsefile.search.load.center.receiver

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.search.load.center.CenterDbUtils
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class CenterDmpPackageReceiverTest {
    private val ACTION_CENTER_DMP_ENABLE_STATUS = "com.oplus.dmp.action.STATUS"
    val DMP_ENABLE_STATUS_AVAILABLE = "available"
    val DMP_ENABLE_STATUS_FATAL = "fatal"
    val CENTER_DMP_PKG_NAME = "com.oplus.dmp"

    private var mContext: Context? = null
    private var mCenterReceiver: CenterDmpPackageReceiver? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mCenterReceiver = CenterDmpPackageReceiver()
    }

    @Test
    fun should_updateEnableValue_when_onReceiveDmpEnable() {
        val intent = Intent(ACTION_CENTER_DMP_ENABLE_STATUS).apply {
            this.putExtra("status", DMP_ENABLE_STATUS_AVAILABLE)
        }
        mCenterReceiver?.onReceive(mContext, intent)

        Assert.assertTrue(CenterDbUtils.getDmpEnableStatus())
    }

    @Test
    fun should_updateEnableValue_when_onReceiveClearPkgData() {
        val intent = Intent(Intent.ACTION_PACKAGE_DATA_CLEARED)
        val mockUri = Mockito.mock(Uri::class.java)
        PowerMockito.`when`(mockUri.schemeSpecificPart).thenReturn(CENTER_DMP_PKG_NAME)
        mCenterReceiver?.onReceive(mContext, intent)
    }
}