/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - ResourceUtils.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/01/24
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9017070 2025/01/24      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.util

import android.content.Context
import android.view.View
import android.view.ViewTreeObserver
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.home.view.group.GroupManageRecyclerAdapter
import com.soundrecorder.common.databean.GroupInfo

object GroupManageUtils {
    private const val TAG = "GroupManageUtils"
    const val NUM_1_UPDATE_OR_INSERT_EXIST: Int = 1
    const val NUM_2_INSERT_SUCCESS: Int = 2
    const val NUM_3_UPDATE_SUCCESS: Int = 3

    object OneShotPostDrawListener {
        @JvmStatic
        fun add(view: View, action: () -> Unit) {
            view.viewTreeObserver?.addOnPreDrawListener(object :
                ViewTreeObserver.OnPreDrawListener {
                override fun onPreDraw(): Boolean {
                    view.viewTreeObserver.removeOnPreDrawListener(this)
                    action()
                    return true
                }
            })
        }
    }

    @JvmStatic
    fun getResIdByResName(context: Context?, resName: String?): Int {
        if (context == null || resName == null) {
            return 0
        }

        val resources = context.resources
        val packageName = context.packageName
        return resources.getIdentifier(resName, "drawable", packageName)
    }

    /**
     * description:跳转到组所在位置
     */
    @JvmStatic
    fun scrollToPosition(
        recordingGroupRv: RecyclerView,
        currentSelectedGroupInfo: GroupInfo?,
        groupManageRecyclerAdapter: GroupManageRecyclerAdapter?
    ): () -> Unit = {
        var position = 0
        groupManageRecyclerAdapter?.values?.apply {
            if (currentSelectedGroupInfo != null) {
                position = indexOf(find { it.groupInfo?.mUuId == currentSelectedGroupInfo.mUuId })
            }
            if (position in 0..lastIndex) {
                (recordingGroupRv as COUIRecyclerView).smoothScrollToPosition(position)
            } else {
                DebugUtil.e(TAG, "position value error:$position")
            }
        }
    }
}