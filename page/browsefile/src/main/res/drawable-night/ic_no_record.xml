<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="280dp"
    android:height="200dp"
    android:viewportWidth="280"
    android:viewportHeight="200">
  <path
      android:pathData="M116.33,32.42C116.33,31.22 115.35,30.24 114.15,30.24C112.95,30.24 111.97,31.22 111.97,32.42V78.42C111.97,79.62 112.95,80.6 114.15,80.6C115.35,80.6 116.33,79.62 116.33,78.42V32.42Z"
      android:fillColor="#D98B84"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M75.8,40.15C77,40.15 77.98,41.12 77.98,42.32V68.59C77.98,69.79 77,70.77 75.8,70.77C74.6,70.77 73.62,69.79 73.62,68.59V42.32C73.62,41.12 74.6,40.15 75.8,40.15Z"
      android:fillColor="#D98B84"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M68.15,43.22C69.35,43.22 70.32,44.2 70.32,45.4V65.51C70.32,66.71 69.35,67.69 68.15,67.69C66.95,67.69 65.97,66.71 65.97,65.51V45.4C65.97,44.2 66.95,43.22 68.15,43.22Z"
      android:fillColor="#BF5C56"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M85.63,48.85C85.63,47.65 84.66,46.68 83.46,46.68C82.26,46.68 81.28,47.65 81.28,48.85V61.98C81.28,63.19 82.26,64.16 83.46,64.16C84.66,64.16 85.63,63.19 85.63,61.98V48.85Z"
      android:fillColor="#BF5C56"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M91.11,50.05C92.31,50.05 93.29,51.03 93.29,52.23V58.76C93.29,59.96 92.31,60.93 91.11,60.93C89.91,60.93 88.93,59.96 88.93,58.76V52.15C89.01,50.95 89.91,50.05 91.11,50.05Z"
      android:fillColor="#D98B84"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M100.94,48.85C100.94,47.65 99.97,46.68 98.77,46.68C97.56,46.68 96.59,47.65 96.59,48.85V61.98C96.59,63.19 97.56,64.16 98.77,64.16C99.97,64.16 100.94,63.19 100.94,61.98V48.85Z"
      android:fillColor="#D98B84"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M106.49,43.45C107.69,43.45 108.67,44.42 108.67,45.62V65.36C108.67,66.56 107.69,67.54 106.49,67.54C105.29,67.54 104.32,66.56 104.32,65.36V45.62C104.32,44.42 105.29,43.45 106.49,43.45Z"
      android:fillColor="#BF5C56"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M121.81,40.15C123.01,40.15 123.98,41.12 123.98,42.32V68.59C123.98,69.79 123.01,70.77 121.81,70.77C120.6,70.77 119.63,69.79 119.63,68.59V42.32C119.7,41.12 120.6,40.15 121.81,40.15Z"
      android:fillColor="#D98B84"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M131.64,45.62C131.64,44.42 130.66,43.45 129.46,43.45C128.26,43.45 127.28,44.42 127.28,45.62V65.36C127.28,66.56 128.26,67.54 129.46,67.54C130.66,67.54 131.64,66.56 131.64,65.36V45.62Z"
      android:fillColor="#BF5C56"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M187.47,76.99C187.47,76.99 184.69,69.94 182.74,67.01C180.64,63.79 174.49,60.18 174.49,60.18L176.74,76.02C177.04,78.8 180.04,80.45 182.52,79.25L187.47,76.99Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M180.87,95.76C180.87,95.76 182.9,95.83 185.75,96.28C188.67,96.73 192.5,97.48 196.25,98.46C198.13,98.98 200.01,99.51 201.73,100.03C203.46,100.63 205.03,101.31 206.38,101.99C207.74,102.66 208.79,103.34 209.54,103.94C210.29,104.46 210.66,104.84 210.66,104.84C213.21,107.24 213.29,111.29 210.89,113.84C210.06,114.67 209.09,115.27 208.11,115.57C208.11,115.57 207.59,115.72 206.68,115.87C205.78,116.02 204.51,116.17 203.01,116.17C201.51,116.17 199.78,116.02 197.98,115.79C196.18,115.49 194.23,115.12 192.35,114.67C188.52,113.84 184.77,112.72 181.99,111.82C179.22,110.84 177.42,110.01 177.42,110.01C173.66,108.21 172.16,103.79 173.89,100.03C175.24,97.26 178.02,95.68 180.87,95.76Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M139.44,129.53C139.07,132.45 137.04,144.61 136.81,145.66C135.61,155.87 130.66,158.49 127.21,157.52C124.28,156.69 122.33,153.99 122.4,151.07C122.4,151.07 122.4,150.32 122.48,149.04C122.56,147.76 122.71,145.89 122.86,143.64C122.93,142.51 123.08,141.31 123.23,140.03C123.38,138.76 123.53,137.41 123.68,136.06C123.98,133.28 124.43,130.35 124.88,127.43C125.33,124.5 126.23,121.72 126.68,118.95C127.21,116.17 127.81,113.69 128.48,111.52C129.08,109.34 129.38,107.39 129.84,106.19C130.96,102.81 133.36,100.78 137.57,101.99C144.62,103.94 139.74,126.6 139.44,129.53Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M131.64,120.6C134.19,124.95 138.02,127.88 142.67,128.55C142.89,126.75 144.4,122.4 146.05,121.42C148.82,119.77 152.5,120.75 154.15,123.52C161.43,118.95 163.76,111.89 159.33,104.39C154.83,96.73 143.95,95.31 136.07,98.98C128.04,102.81 127.13,112.94 131.64,120.6Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M178.32,96.66L173.29,96.21C171.49,96.06 169.76,95.61 168.11,94.78L167.29,94.41C166.16,93.88 164.96,94.71 164.96,95.91V99.66H178.24V96.66H178.32Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M142.22,97.18L147.62,96.21C149.42,96.06 151.15,95.61 152.8,94.78L153.63,94.41C154.75,93.88 155.95,94.71 155.95,95.91V99.66H142.67L142.22,97.18Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M186.49,74.22C186.49,74.22 204.28,93.5 205.03,94.41C206.53,96.21 207.88,97.93 208.93,99.43C211.11,102.43 212.24,104.69 212.24,104.69C214.11,108.29 212.61,112.79 209.01,114.59C206.08,116.09 202.71,115.42 200.45,113.24C200.45,113.24 198.65,111.44 196.48,108.44C195.35,106.94 194.15,105.14 192.87,103.19C192.2,102.21 191.6,101.23 190.92,100.18C190.25,99.13 186.95,92.6 185.74,90.58C184.54,88.55 179.74,79.47 179.74,79.47C178.76,77.14 179.44,74.37 181.77,73.39C183.42,72.79 185.22,73.17 186.49,74.22Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M139.89,167.43C154.08,167.43 168.26,167.43 182.44,167.43C176.44,150.84 183.04,134.26 184.62,117.67C185.74,106.04 173.59,96.58 164.13,96.58C162.18,96.58 160.23,96.58 158.28,96.58C148.75,96.58 136.59,106.04 137.79,117.67C139.22,134.26 145.82,150.84 139.89,167.43Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M190.13,106.57C191.3,101.65 188.25,96.72 183.33,95.56C178.41,94.39 173.48,97.44 172.31,102.36C171.15,107.28 174.2,112.21 179.12,113.38C184.04,114.54 188.97,111.49 190.13,106.57Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M106.27,104.39L104.1,106.64"
      android:strokeAlpha="0.4"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:fillAlpha="0.4"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.89,107.46L107.02,103.79L107.1,103.71C107.25,103.64 107.4,103.56 107.55,103.49L111.15,102.96C111.6,102.89 111.97,103.19 112.05,103.64C112.13,104.09 111.82,104.46 111.45,104.54L107.85,105.36L108.3,105.14L104.39,109.11C103.94,109.57 103.27,109.57 102.82,109.11C102.37,108.74 102.37,107.99 102.89,107.46C102.82,107.54 102.82,107.54 102.89,107.46Z"
      android:fillColor="#CCCCCC"/>
  <path
      android:pathData="M180.64,104.46C181.62,106.71 182.59,108.89 183.57,111.14"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.96,86.6C170.74,86.15 177.49,77.44 177.12,68.36C177.12,67.54 177.04,66.79 177.04,65.96C176.66,56.96 169.31,50.95 160.6,51.4C159.78,51.48 158.88,51.48 158.05,51.48C149.27,51.78 142.52,58.31 142.89,67.39C143.34,77.82 151.9,87.13 161.96,86.6Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M153.4,101.01C157.98,101.68 162.55,101.68 167.13,101.01C166.68,94.41 166.68,87.88 167.13,81.27C162.55,80.6 157.98,80.6 153.4,81.27C153.85,87.8 153.85,94.41 153.4,101.01Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M135.54,151.29C134.71,154.44 132.09,159.47 127.36,159.47C123.31,159.47 119.93,156.69 119.03,152.94C117.08,147.91 113.03,137.11 112.88,136.81C111.97,134.63 111,132.38 110.1,130.35C109.2,128.33 108.3,126.45 107.55,124.8C106.2,121.87 105.22,119.85 105.07,119.47C104.77,119.02 104.47,118.57 104.32,118.04L100.04,105.51L107.92,105.96L113.25,108.36C113.25,108.36 114.38,103.49 114.75,102.43C115.35,100.93 117.08,101.08 117.23,101.46C117.38,101.83 117.3,106.79 116.78,110.69C116.48,113.24 116.48,115.79 116.1,117.67C116.48,118.27 116.85,118.87 117.3,119.55C118.28,121.05 119.33,122.85 120.61,124.72C121.81,126.6 123.16,128.63 124.51,130.58C125.18,131.55 125.78,132.6 126.46,133.58C127.13,134.55 127.73,135.53 128.34,136.51C129.46,138.38 131.11,139.88 131.94,141.46C132.31,142.29 136.21,148.59 135.54,151.29Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M185.22,117.97C183.95,136.13 176.91,151.86 182.91,167.39L139.87,167.65C145.8,152.04 138.69,136.06 137.49,117.97C136.51,107.01 144.4,105.74 153.4,105.96L148.52,96.51C148.45,96.28 148.52,96.06 148.67,95.98L153.18,93.88C153.48,93.73 153.85,93.8 154.08,94.03L160.3,100.63L166.53,94.03C166.76,93.8 167.13,93.73 167.43,93.88L171.94,95.98C172.16,96.06 172.24,96.28 172.09,96.51L167.13,106.04C177.04,105.59 186.27,106.19 185.22,117.97Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M182.07,163.6L188.67,187.76H133.51L140.87,163.52C141.24,162.47 142.07,161.8 143.12,161.8H179.82C180.79,161.8 181.77,162.55 182.07,163.6Z"
      android:fillColor="#CC8A52"/>
  <path
      android:pathData="M160.3,100.71L164.51,105.59"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.26,73.62C168.41,76.99 170.59,79.55 172.99,79.4C175.39,79.25 177.26,76.47 177.04,73.09C176.89,69.71 174.71,67.16 172.31,67.31C169.91,67.46 168.11,70.32 168.26,73.62Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.76,73.54C169.99,76.84 172.16,79.47 174.49,79.32C176.82,79.17 179.59,76.39 179.44,73.02C179.22,69.64 176.14,67.16 173.81,67.24C171.49,67.39 169.68,70.17 169.76,73.54Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M171.04,58.46C179.29,61.08 177.34,77.22 177.04,78.95C175.84,86.38 170.96,91.93 165.78,91.4C158.65,90.65 152.05,83.67 150.7,76.24C150.47,74.89 149.87,71.59 151.3,67.91C153.85,61.01 163.31,55.91 171.04,58.46Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M164.28,83.97C166.16,82.17 169.76,80.45 170.21,84.05C170.13,84.72 169.84,85.85 167.88,85.93C166.16,86 163.83,85.25 164.28,83.97Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M169.61,80.22C168.86,80.15 168.11,79.77 167.58,79.25"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M174.19,74.14C174.19,74.89 173.74,75.49 173.14,75.49C172.61,75.49 172.16,74.89 172.16,74.14C172.16,73.39 172.61,72.79 173.21,72.79C173.74,72.79 174.19,73.39 174.19,74.14Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M163.98,74.37C163.98,75.12 163.46,75.79 162.93,75.79C162.33,75.79 161.88,75.12 161.88,74.37C161.88,73.62 162.41,72.94 162.93,72.94C163.53,73.02 164.06,73.62 163.98,74.37Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M175.46,72.04C175.39,71.89 174.56,71.07 173.36,71.29C172.31,71.44 171.79,72.27 171.64,72.42"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M159.71,72.57C159.85,72.42 160.68,71.52 162.11,71.59C163.31,71.67 163.98,72.42 164.13,72.57"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.81,84.2C164.81,84.2 168.11,82.77 168.71,83.07C169.31,83.45 169.46,84.2 169.46,84.2H164.81Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#B9B9BA"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.81,67.24C171.34,67.61 172.31,68.06 173.59,68.29C174.34,68.36 175.01,68.36 175.54,68.21"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.13,69.19C158.43,68.89 159.63,67.54 161.66,67.31C163.61,67.09 165.03,67.99 165.41,68.21"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M152.2,69.26C150.85,68.51 148.75,68.59 147.7,69.71C146.2,71.29 147.55,74.44 149.12,76.09C149.35,76.32 151.3,78.42 152.65,77.89C154.45,77.07 155.13,70.99 152.2,69.26Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M148.75,71.74C149.12,71.37 149.72,71.22 150.25,71.37C150.77,71.44 151.23,71.82 151.6,72.19C151.98,72.57 152.2,73.09 152.5,73.54"
      android:strokeAlpha="0.4"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:fillAlpha="0.4"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M145.44,36.84C145.22,37.37 143.34,42.47 146.35,47.28C148.6,50.8 152.65,52.83 156.93,52.6"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M146.2,44.2C147.17,51.03 153.18,54.48 159.78,54.63C166.46,54.93 172.39,51.48 173.36,44.12C174.34,36.85 168.26,31.37 159.78,31.82C151.37,31.89 145.22,37.37 146.2,44.2Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M118.5,106.41V116.54C118.5,117.82 117.53,118.79 116.25,118.87H107.1C105.82,118.79 104.85,117.82 104.85,116.54V106.41C104.85,105.21 105.67,104.31 106.8,104.09C106.95,104.09 107.02,104.09 107.17,104.09H116.18C116.33,104.09 116.4,104.09 116.55,104.09C117.68,104.31 118.5,105.29 118.5,106.41Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.65,115.27H110.62C108.52,115.27 106.72,113.54 106.72,111.37V95.16C106.72,93.05 108.45,91.25 110.62,91.25H112.65C114.75,91.25 116.55,92.98 116.55,95.16V111.37C116.55,113.54 114.83,115.27 112.65,115.27Z"
      android:fillColor="#BF5C56"/>
  <path
      android:pathData="M116.03,100.48C116.1,101.01 115.73,101.46 115.28,101.61L109.35,103.11L106.65,106.64L107.32,106.04L107.4,105.96C107.55,105.81 107.7,105.74 107.92,105.74L111.6,105.21C112.12,105.14 112.65,105.51 112.72,106.04C112.8,106.56 112.42,107.01 111.97,107.16L108.75,107.91L105.07,111.67C104.54,112.19 103.64,112.19 103.12,111.67C102.67,111.22 102.59,110.46 102.89,109.94C102.59,109.94 102.22,109.79 101.92,109.57C101.47,109.19 101.24,108.74 101.17,108.14C101.09,108.14 101.09,108.06 101.09,108.06C100.27,107.46 100.04,106.34 100.57,105.51C99.59,105.14 99.14,104.01 99.52,103.04C99.59,102.96 99.59,102.89 99.67,102.81L102.74,97.56L102.82,97.41C102.89,97.33 102.97,97.26 103.04,97.18L108.52,92.68C108.97,92.3 109.65,92.38 109.95,92.83C110.32,93.2 110.25,93.8 109.87,94.18L105.29,99.06L104.32,101.39L105.67,99.88L105.75,99.81C105.89,99.66 106.12,99.51 106.35,99.43L114.83,97.26C115.43,97.11 116.03,97.48 116.18,98.08C116.33,98.68 116.03,99.21 115.43,99.43L114.68,99.73H114.83C115.43,99.58 115.95,99.96 116.03,100.48Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M177.19,77.59C177.11,78.2 177.04,78.65 177.04,78.87C175.84,86.3 170.96,91.85 165.78,91.33C161.88,90.95 158.2,88.7 155.43,85.55"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M153.55,83.07C153.7,86.3 153.77,90.58 153.7,93.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.34,77.82C177.79,78.57 179.82,79.55 179.82,79.55C181.17,82.62 187.77,94.11 189.42,96.88C189.35,96.88 188.75,96.81 188.67,96.73C186.12,96.28 183.79,95.38 182.07,95.31C178.09,95.08 176.44,96.51 175.54,96.43L171.86,95.98"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M175.99,61.08C175.99,61.08 180.64,63.71 182.74,67.01C183.87,68.66 185.22,71.74 186.2,73.99C186.27,74.07 205.18,94.56 206.08,95.53C207.66,97.26 217.49,107.76 210.66,114.22C207.58,117.07 202.63,116.24 200,116.02C198.73,115.87 187.85,113.62 184.77,112.64"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M189.42,96.88C189.42,96.88 191.97,101.23 193.02,103.11C194.15,105.14 196.63,107.76 196.63,107.76"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M169.31,58.01C168.63,61.08 166.53,63.79 163.68,65.21C161.73,66.19 159.48,66.56 157.3,67.01C155.43,67.46 153.25,67.91 151.75,68.89C151.75,68.89 151.45,68.74 151.22,68.66C151.15,65.74 154.15,57.63 161.58,55.76C165.11,54.86 166.98,56.43 169.31,58.01Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M177.87,71.07C177.87,73.47 177.79,75.79 177.41,78.12H177.34C177.57,75.79 177.41,73.47 177.26,71.14C177.04,68.81 176.59,66.56 175.84,64.39C175.09,62.29 173.81,60.26 172.01,59.28C171.79,59.13 171.56,59.06 171.34,58.98C171.11,58.91 170.88,58.83 170.59,58.76C170.06,58.61 169.54,58.53 168.93,58.38C167.81,58.23 166.76,58.16 165.63,58.16C163.46,58.23 161.28,58.68 159.33,59.58C157.38,60.48 155.73,61.83 154.38,63.49C153.7,64.31 153.1,65.21 152.57,66.11C152.35,66.56 152.05,67.09 151.9,67.54C151.67,68.06 151.52,68.44 151.37,68.89C151.3,69.04 151.3,69.26 151.22,69.41C151.15,69.79 151.07,70.24 151.07,70.62C152.12,71.59 152.95,73.09 153.25,74.89C153.63,77.97 152.42,80.75 150.4,81.57C150.17,81.65 149.87,81.72 149.65,81.8C147.25,82.17 143.79,79.92 143.27,76.39C142.82,73.24 144.92,70.39 147.1,69.56C147.25,68.89 147.4,68.21 147.62,67.61C147.85,66.94 148.15,66.26 148.45,65.74C148.75,65.21 149.05,64.61 149.42,64.09C150.1,63.04 150.92,61.99 151.82,61.08C153.63,59.21 155.8,57.78 158.2,56.88C160.6,55.98 163.16,55.61 165.63,55.76C166.91,55.83 168.11,56.06 169.31,56.36C169.91,56.51 170.51,56.66 171.04,56.88C171.34,57.03 171.64,57.11 171.94,57.26C172.24,57.41 172.54,57.56 172.84,57.78C173.96,58.53 174.79,59.51 175.46,60.63C176.14,61.68 176.51,62.89 176.89,64.01C177.57,66.34 177.79,68.66 177.87,71.07Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.3,81.57C151.07,81.57 150.77,81.65 150.55,81.57C152.5,80.75 153.77,77.97 153.4,74.89C153.1,73.17 152.27,71.59 151.22,70.62C151.22,70.16 151.3,69.79 151.37,69.41C153.1,70.16 154.52,72.12 154.9,74.67C155.35,78.12 153.77,81.2 151.3,81.57Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M148.9,75.64C149.05,76.92 148.3,78.05 147.25,78.12C146.2,78.2 145.29,77.22 145.15,75.94C144.99,74.67 145.74,73.54 146.8,73.47C147.85,73.39 148.82,74.37 148.9,75.64Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.2,88.33C158.2,88.33 160.98,92.08 163.38,92.83C165.86,93.58 166.68,93.2 166.68,93.2V91.4C166.68,91.4 165.78,91.7 163.31,90.8C160.83,89.9 158.2,88.33 158.2,88.33Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M166.76,91.33L166.83,93.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.91,129.9C123.91,129.9 126.23,133.58 130.36,139.21C133.14,142.96 133.89,148.51 133.89,148.51"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.9,99.66L107.47,101.31L103.72,105.66"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.57,106.64L105.44,107.76"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M139.22,130.73C138.84,133.65 136.96,144.54 136.82,145.66C135.46,155.19 131.56,159.47 127.36,159.47C123.31,159.47 119.93,156.69 119.03,152.94C117.08,147.91 113.03,137.11 112.88,136.81C111.97,134.63 111,132.38 110.1,130.35C109.2,128.33 108.3,126.45 107.55,124.8C106.2,121.87 105.22,119.85 105.07,119.47C104.77,119.02 104.47,118.57 104.32,118.04L100.04,105.51C100.04,105.51 99.29,104.01 99.52,103.11C99.59,103.04 102.82,97.48 102.82,97.48C102.89,97.41 102.97,97.33 103.04,97.26L108.52,92.75C108.97,92.38 109.65,92.45 109.95,92.9C110.32,93.28 110.25,93.88 109.87,94.26L105.29,99.13L104.32,101.46L105.82,99.81C105.97,99.66 106.2,99.51 106.42,99.43L114.9,97.26C115.5,97.11 116.1,97.48 116.25,98.08C116.4,98.68 116.1,99.21 115.5,99.43L114.75,99.73H114.9C115.43,99.66 115.95,100.03 116.03,100.63C116.1,101.16 115.73,101.61 115.28,101.76L109.35,103.26L106.65,106.79L107.4,106.11C107.55,105.96 107.7,105.89 107.92,105.89L111.6,105.36C112.12,105.29 112.65,105.66 112.72,106.19C112.8,106.71 112.42,107.16 111.97,107.31L108.75,108.06L104.92,112.04"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.85,118.87C117.83,120.37 119.33,122.85 120.53,124.72C121.73,126.6 123.01,128.55 124.36,130.5C124.51,129.45 124.66,128.48 124.81,127.43C125.26,124.5 129.38,107.46 129.84,106.19C131.49,101.46 135.39,98.08 141.92,97.26C143.19,97.11 148.3,96.21 148.3,96.21"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.55,101.24C116.85,101.24 117.08,101.39 117.15,101.46C117.23,101.61 117.23,102.81 117.23,104.31"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.32,101.46L102.52,103.64"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.22,129.68C101.69,153.69 115.58,148.81 119.33,159.62C121.5,165.7 117.08,169.9 114.82,172C112.5,174.1 110.7,175.98 108.97,178.61C107.32,181.23 106.42,184.61 107.62,187.46"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.45,135.53H108.97C107.77,135.53 106.87,134.55 106.87,133.43V132.98H116.63V133.43C116.55,134.55 115.58,135.53 114.45,135.53Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M116.25,118.87V120.97C116.25,121.87 115.5,122.7 114.6,122.7H114.53V126.9C114.53,128.25 114,129.3 113.18,129.83V187.84H110.1V129.83C109.27,129.3 108.75,128.33 108.82,126.9C108.82,125.47 108.82,124.12 108.82,122.7H108.75C107.85,122.7 107.1,121.95 107.1,120.97V118.87H116.25Z"
      android:fillColor="#595050"/>
</vector>
