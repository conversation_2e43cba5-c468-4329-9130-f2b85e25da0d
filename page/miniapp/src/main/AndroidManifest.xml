<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.soundrecorder.miniapp">

    <application>
        <!--android:icon:MiniApp 图标（如 activity 未配置则读取 application 的 icon），用于资源库展示-->
        <!--android:label MiniApp 标题 （必填）-->
        <activity
            android:name=".MiniRecorderActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:icon="@drawable/ic_launcher_recorder"
            android:label="@string/app_name_main"
            android:launchMode="singleTask"
            android:permission="com.oplus.permission.safe.ASSISTANT"
            android:taskAffinity="${applicationId}.OtherDisplay"
            android:theme="@style/AppBaseTheme.NoActionBar">

            <intent-filter>
                <!--SecondaryHome 的资源库列表启动 Action （必填）-->
                <action android:name="oplus.intent.action.MINI_LAUNCHER_MAIN" />
                <!--支持在 SecondaryHome 外屏显示 （必填）-->
                <category android:name="android.intent.category.SECONDARY_HOME" />
                <!--支持在 SecondaryHome 的资源库列表显示 （必填）-->
                <category android:name="oplus.intent.category.MINI_LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <!--SecondaryHome 的入口小组件启动 Action （必填）-->
                <action android:name="oplus.intent.action.WIDGET_ENTRY" />
                <!--Activity支持在 SecondaryHome 外屏显示 （必填）-->
                <category android:name="android.intent.category.SECONDARY_HOME" />
            </intent-filter>
            <!--入口组件图标，用于百变视窗小组件；如未配置，则不会提供该 MiniApp 的入口组件供用户选择-->
            <!--图标尺寸：24dp*24dp，只需提供中间图标部分，不包含圆形背景；-->
            <meta-data
                android:name="com.oplus.secondaryhome.entry.widget.icon"
                android:resource="@drawable/ic_entry_widget_icon" />
            <!--小组件名称，用于百变视窗小组件选择列表中显示使用-->
            <meta-data
                android:name="com.oplus.secondaryhome.entry.widget.title"
                android:resource="@string/app_name_main" />
        </activity>

    </application>

</manifest>